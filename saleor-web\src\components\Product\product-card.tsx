"use client";
import React, { useEffect, useState } from "react";
import { Link } from "@/navigation";
import { useTranslations } from "next-intl";
import { defaultLocale } from "@/config";
import { Button } from "../Button";
import { Placeholder } from "../Placeholder";
import { ProductListItemFragment } from "@/gql/graphql";
import { useLoveStore } from "@/lib/store/love.store";
import clsx from "clsx";
import Svg from "../Header/Menu/Svg";
import Lightbox from "react-image-lightbox";
import "react-image-lightbox/style.css"; // 需要引入样式
import { motion } from "framer-motion";
import { containerVariants, itemVariants, productTranslationName } from "@/lib/utils/util";
import { Tooltip, App } from "antd";
import { useCompareStore } from "@/lib/store/Compare.store";
import CompareList from "../CompareList";
import Image from "next/image";
import { BodyText, handlerInnerHtml } from "../BodyText";
import SEOOptimizedImage from "@/components/Image/SEOOptimizedImage";
import { useShoppingCart } from "@/lib/hooks/useShoppingCart";
import { useShoppingCartStore } from "@/lib/store/shoppingCart";
import { useRouter } from "@/navigation";
export type ImgObj = {
	id?: number;
	url: string;
	name: string;
	type: string;
};
export type Metadata = { __typename?: "MetadataItem" | undefined; key: string; value: string };
export const filterCateImg = (metadataList: Metadata[]) => {
	let imgUrlList: ImgObj[] = [];
	if (metadataList && metadataList.length > 0) {
		const media = metadataList.filter((i: Metadata) => i.key === "media");
		if (media.length > 0) imgUrlList = JSON.parse(media[0].value) as ImgObj[];
	}
	return imgUrlList;
};
type dscObj = {
	data: { text: string };
	type: string;
};
type dscList = {
	blocks: dscObj[];
};

// 动画配置
const containerHoverVariants = {
	hidden: { opacity: 0 },
	visible: {
		opacity: 1,
		transition: {
			staggerChildren: 0.1, // 子元素依次延迟显示
			delayChildren: 0.2, // 动画整体延迟
		},
	},
};

const iconVariants = {
	hidden: { opacity: 0, y: 20 },
	visible: { opacity: 1, y: 0 },
};

export const filterSortDsc = (dsc: string) => {
	let sortDsc = "";
	let longDsc = "";
	if (dsc) {
		const dscObj = JSON.parse(dsc) as dscList;

		if (dscObj && dscObj.blocks.length > 0) {
			sortDsc = dscObj.blocks[1].data.text;
			longDsc = dscObj.blocks[0].data.text;
		}
	}

	return { sortDsc, longDsc };
};
export default function ProductCard({
	productItem,
	locale,
	isSelectMode = false,
	isSelected = false,
	onSelect,
}: {
	productItem: ProductListItemFragment;
	locale: string;
	isSelectMode?: boolean;
	isSelected?: boolean;
	onSelect?: () => void;
}) {
	const [isOpen, setIsOpen] = useState(false);
	const [imageUrl, setimageUrl] = useState(""); // 替换为你的图片 URL
	let { compareIds, show, changeShow, setcCmpareProducts } = useCompareStore();
	const t = useTranslations();
	const { createBuyBowCheckout } = useShoppingCart();
	const { setisCart } = useShoppingCartStore() as any;
	const router = useRouter();
	const { message } = App.useApp();
	const [buyNowLoading, setBuyNowLoading] = useState(false);

	const pt = productItem.media;
	const imgs = JSON.parse(pt) || [] as any;
	const [currentImageIndex, setCurrentImageIndex] = useState(0);

	const [isHovered, setIsHovered] = useState(false); // 控制动画容器状态
	const handleMouseEnter = () => {
		setIsHovered(true);
		// 只有当存在第二张图片时才切换
		if (imgs.length > 1 && imgs[1]?.url) {
			setCurrentImageIndex(1); // 切换到第二张图片
		}
	};
	const handleMouseLeave = () => {
		setIsHovered(false);
		setCurrentImageIndex(0); // 切换回第一张图片
	};

	// 添加对比
	const addOrDelCompare = (event: React.MouseEvent, product) => {
		event.stopPropagation();
		console.log(product, "product");

		const id = product?.id;
		if (id) {
			setcCmpareProducts(id);
			changeShow(true);
		}
	};
	let html =
		locale == defaultLocale
			? filterSortDsc(productItem?.descriptionJson).sortDsc
			: filterSortDsc(productItem?.translation?.descriptionJson).sortDsc ||
			filterSortDsc(productItem?.descriptionJson).sortDsc;
	let ProductName = locale === defaultLocale
		? productTranslationName(productItem.name)
		: productTranslationName(productItem.translation)
			? productTranslationName(productItem.translation.name)
			: productTranslationName(productItem.name)

	// 处理卡片点击
	const handleCardClick = (e: React.MouseEvent) => {
		if (isSelectMode && onSelect) {
			e.preventDefault();
			e.stopPropagation();
			onSelect();
		}
	};

	// 立即购买处理函数 - 包含库存验证
	const handleBuyNow = async (e: React.MouseEvent) => {
		e.preventDefault();
		e.stopPropagation();

		// 获取第一个变体
		const firstVariant = productItem.variants?.[0];
		// if (!firstVariant) {
		// 	message.error(t('message.noVariant') || '该产品暂无可用变体');
		// 	return;
		// }

		// 检查库存
		// const stockQuantity = firstVariant.quantityAvailable || 0;
		// if (stockQuantity <= 0) {
		// 	message.error(t('nav.OUT OF STOCK'));
		// 	return;
		// }

		// 检查变体是否有价格
		// if (!firstVariant.pricing?.price?.gross?.amount) {
		// 	message.error(t('message.noPrice') || '该产品暂无价格信息');
		// 	return;
		// }

		setBuyNowLoading(true);
		try {
			// 创建立即购买的结账
			const success = await createBuyBowCheckout({
				channel: "default-channel",
				selectedVariantID: firstVariant.id,
				quantity: 1,
			});

			if (success) {
				setisCart(false);
				router.push("/checkout");
			}
		} catch (error) {
			console.error('Buy now error:', error);
			message.error('Buy now error');
		} finally {
			setBuyNowLoading(false);
		}
	};

	return (
		<div
			onMouseEnter={handleMouseEnter}
			onMouseLeave={handleMouseLeave}
			onClick={handleCardClick}
			className={`group relative flex flex-col overflow-hidden rounded bg-white transition-all duration-500  ${isSelectMode ? 'cursor-pointer' : ''
				} ${isSelected ? 'ring-2 ring-blue-500 ring-offset-4' : ''}`}
		>
			{/* DJI风格的产品图片容器 */}
			<div className="relative w-[300px] h-[300px] mx-auto overflow-hidden bg-white">
				<Link href={"/product/" + productItem.slug} className="block h-full w-full">
					{/* 桌面端图片 */}
					<div className="h-full w-full">
						<SEOOptimizedImage
							width={800}
							height={800}
							quality={100}
							alt={ProductName}
							className="h-full w-full object-cover transition-all duration-700 ease-in-out will-change-transform"
							src={imgs[0]?.url || "/image/default-image.webp"}
						/>
						{/* {currentImageIndex == 0 || !imgs[1]?.url ? (
							<SEOOptimizedImage
								width={800}
								height={800}
								quality={100}
								alt={ProductName}
								className="h-full w-full object-contain transition-all duration-700 ease-in-out will-change-transform"
								src={imgs[0]?.url || "/image/default-image.webp"}
							/>
						) : (
							<SEOOptimizedImage
								width={800}
								height={800}
								quality={100}
								alt={ProductName}
								className="h-full w-full object-contain transition-all duration-700 ease-in-out will-change-transform group-hover:scale-105 p-6"
								src={imgs[1]?.url || "/image/default-image.webp"}
							/>
						)} */}
					</div>

					{/* 移动端图片 */}
					{/* <img
						className="h-full w-full object-contain p-4 md:hidden"
						src={imgs[currentImageIndex == 0 || !imgs[1]?.url ? 0 : currentImageIndex]?.url || "/image/default-image.webp"}
						alt={ProductName}
					/> */}

					{/* DJI风格的悬停遮罩 */}
					{/* <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 transition-all duration-500 group-hover:opacity-100" /> */}
				</Link>
			</div>

			{/* DJI风格的产品信息区域 */}
			<div className="flex flex-1 flex-col gap-3 p-6 max-md:gap-2 max-md:p-4">
				{/* 产品名称 */}
				<div className="flex items-start justify-between gap-3">
					<h2
						className="line-clamp-1 flex-1 text-[18px] text-[#262626] transition-colors duration-300 text-center"
					>
						{ProductName}
					</h2>
				</div>

				{/* 价格显示 */}
				{/* <div className="mb-2">
					{renderPrice(productItem.pricing?.priceRange)}
				</div> */}

				{/* 产品描述 - 桌面端显示 */}
				<div className="flex-1 max-md:hidden">
					<p
						className="line-clamp-4 text-[12px] leading-relaxed text-[#545454] text-center"
						dangerouslySetInnerHTML={{ __html: handlerInnerHtml(html) }}
					></p>
				</div>

				{/* DJI风格的查看详情按钮 */}
				<div className="pt-4 max-md:pt-2 flex justify-center items-center gap-4">
					{/* <button
						onClick={handleBuyNow}
						disabled={buyNowLoading}
						className="w-full px-2 py-[12px] bg-[#262626] rounded-full text-center text-white hover:text-white hover:bg-opacity-80 disabled:bg-gray-400 disabled:cursor-not-allowed transition-all duration-300"
					>
						{buyNowLoading ? (
							<span className="flex items-center justify-center gap-2">
								<i className="ri-loader-4-line animate-spin"></i>
								{t("common.loading")}
							</span>
						) : (
							t("common.Buy_Now")
						)}
					</button> */}
					<Link href={`/product/${productItem.slug}`} className="block w-full px-2 py-[12px] bg-[#f5f5f5] rounded-full text-center text-[#262626] hover:text-[#262626] hover:bg-[#f0f0f0]">
						{t("common.Learn_more")}
					</Link>
					<span className="text-[#262626] cursor-pointer">
						<IconLove product={productItem} />
					</span>
				</div>
			</div>
			{isOpen && <Lightbox mainSrc={imageUrl} onCloseRequest={() => setIsOpen(false)} />}
		</div>
	);
}

export function IconLove(props: { product?: any }) {
	let product = props.product;

	const [isLove, setIsLove] = useState(false);
	const { loveIds, setLoveProducts } = useLoveStore();
	const t = useTranslations();

	// console.log(loveIds,'product');
	useEffect(() => {
		if (loveIds.length && product?.id) {
			setIsLove(loveIds.includes(product.id));
		} else {
			setIsLove(false);
		}
	}, [loveIds]);
	const addOrDelLike = (event: React.MouseEvent) => {
		event.stopPropagation(); //
		const id = product?.id;
		if (id) {
			setLoveProducts(id);
		}
	};
	return (
		<i
			onClick={addOrDelLike}
			className={clsx(
				" text-xl text-[#000]",
				isLove ? " ri-heart-fill !text-[#e08584]" : "ri-heart-line !text-[#000]",
			)}
		></i>
	);
}
export function IconCompare(props: { product?: any }) {
	let product = props.product;

	const [isCompare, setIsCompare] = useState(false);
	const { compareIds, setcCmpareProducts, changeShow } = useCompareStore();
	const t = useTranslations();

	// console.log(loveIds,'product');
	useEffect(() => {
		if (compareIds.length && product?.id) {
			setIsCompare(compareIds.includes(product.id));
		} else {
			setIsCompare(false);
		}
	}, [compareIds]);
	// 添加对比
	const addOrDelCompare = (event: React.MouseEvent, product) => {
		event.stopPropagation();

		const id = product?.id;
		if (id) {
			setcCmpareProducts(id);
		}

		changeShow(true);
	};
	return (
		<i
			onClick={(e) => addOrDelCompare(e, product)}
			className={clsx(
				" text-xl text-[#000]",
				isCompare ? " ri-delete-bin-fill" : "ri-heart-line !text-[#000]",
			)}
		></i>
	);
}

const renderPrice = (price) => {
	// 添加空值检查
	if (!price?.start || !price?.stop) return null;

	try {
		// 获取价格信息
		const startPrice = price.start.gross;
		const stopPrice = price.stop.gross;
		const currency = startPrice.currency || stopPrice.currency || '$';

		// 比较价格是否相同
		const isSamePrice = Number(startPrice.amount) === Number(stopPrice.amount);

		// 价格格式化，保留两位小数
		const formatPrice = (amount) => {
			return typeof amount === 'number'
				? amount.toFixed(2)
				: typeof amount === 'string'
					? parseFloat(amount).toFixed(2)
					: '0.00';
		};

		if (isSamePrice) {
			// 如果价格相同，只显示一个价格
			return (
				<div className="flex items-center">
					<span className="font-medium text-mainColor">
						{currency} {formatPrice(startPrice.amount)}
					</span>
				</div>
			);
		} else {
			// 显示价格范围
			return (
				<div className="flex items-center space-x-2 max-md:flex-col max-md:items-start max-md:space-x-0">
					<span className="font-medium text-mainColor">
						{currency} {formatPrice(startPrice.amount)} - {formatPrice(stopPrice.amount)}
					</span>
				</div>
			);
		}
	} catch (error) {
		console.error("Error rendering price:", error);
		return <span className="text-gray-500">Error rendering price</span>;
	}
};
