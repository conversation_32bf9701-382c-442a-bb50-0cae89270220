import React from "react";
import { type Metadata } from "next";
import { type MyPageProps } from "@/lib/@types/base";
import { getBlogList } from "@/lib/api/blog";
import { type Blog } from "@/lib/@types/api/blog";
import { getBasePageSeo } from "@/lib/api/seo";
import { generateSeo } from "@/lib/utils/seo";
import { unstable_setRequestLocale } from "next-intl/server";
import Banner from "@/components/Home/banner";
import Sponsor from "@/components/ZC/Sponsor";
import Category from "@/components/ZC/Category";
import { executeGraphQL } from "@/lib/graphql";
import { defaultLocale, handleGraphqlLocale } from "@/lib/utils/util";
import { ProductCategoriesDocument } from "@/gql/graphql";
import HomeProducts from "@/components/ZC/HomeProducts";
import { getChannelLanguageMap } from "@/lib/api/channel";
import { fetchProductData } from "@/lib/api/product";
import ProductCategory from "@/components/ZC/ProductCategory";
import IndustryApplication from "@/components/ZC/IndustryApplication";
import AboutUs from "@/components/ZC/AboutUs";
/**
 * 生成页面的元数据
 * @param props 页面的属性，用于生成SEO信息
 * @returns 返回页面的元数据，包括SEO信息
 */
export const generateMetadata = async (props: MyPageProps): Promise<Metadata> => {
	// 获取基础页面的SEO数据
	const seo = await getBasePageSeo(props);
	// 生成最终的SEO信息，并返回
	return generateSeo(props, {
		...seo,
		ogType: "website",
	});
};

// 异步获取博客列表，确保是一个 Promise
export async function fetchBlogList(
	Params: Blog.GetBlogListParams,
): Promise<{ blogList: Blog.BlogListItem[]; blogCount: number }> {
	try {
		const res = await getBlogList(Params);
		return { blogList: res.detail.blog_list, blogCount: res.detail.blog_filter_count };
	} catch (error) {
		console.error("Error fetching blog list:", error);
		return { blogList: [], blogCount: 0 }; // 如果发生错误，返回一个空数组
	}
}

export default async function Page(props: MyPageProps) {
	unstable_setRequestLocale(props.params.locale);

	//获取商品
	const channel = (await getChannelLanguageMap())[defaultLocale];
	//分类
	let categories = { edges: [] } as any;
	try {
		const result = await executeGraphQL(ProductCategoriesDocument, {
			withAuth: false,
			variables: { locale: handleGraphqlLocale(props.params.locale || defaultLocale), first: 10 },
			revalidate: 60
		});
		// 确保返回的数据有效
		categories = result?.categories || { edges: [] };
	} catch (error) {
		// 记录错误但不阻止页面渲染
		console.error("Error fetching product categories:", error);
		// 使用空数组作为默认值
		categories = { edges: [] };
	}

	return (
		<>
			{/* <TDBanner></TDBanner> */}
			<Banner />
			<ProductCategory locale={props.params.locale} categories={categories} useStaticData={false} />
			<IndustryApplication />
			<AboutUs />
		</>
	);
}
