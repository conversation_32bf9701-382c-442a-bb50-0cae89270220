"use client";
import React from "react";
import Image from "next/image";
import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay, Navigation } from "swiper/modules";
import {useRouter  } from "@/navigation";
import { useCategory } from "@/context/CategoriesContext";
import { defaultLocale } from "@/config";

export type Metadata = { __typename?: "MetadataItem" | undefined, key: string, value: string }


type ImgObj = {
	id?: number;
	url: string;
	name: string;
	type: string;
}
export const filterCateImg = (metadataList: Metadata[]) => {
	let imgUrlList: ImgObj[] = [];
	if (metadataList && metadataList.length > 0) {
		const media = metadataList.filter((i: Metadata) => i.key === "media");
		if (media.length > 0) imgUrlList = JSON.parse(media[0].value) as ImgObj[];
	}
	return imgUrlList;
};



interface Props {
	props: string;
	className:string;
}

const Collection: React.FC<Props> = ({ props,className }) => {
	const locale=props||defaultLocale
	const router = useRouter();
	const { categories } = useCategory();

	const handleTypeClick = (type: string) => {
		router.push(`/shop/breadcrumb1?type=${type}`);
	};


	return (
		<>
			<div className={`collection-block  ${className}`}>
				<div className="list-collection section-swiper-navigation px-4 sm:px-5">
					<Swiper
						spaceBetween={12}
						slidesPerView={2}
						navigation
						loop={true}
						modules={[Navigation, Autoplay]}
						breakpoints={{
							576: {
								slidesPerView: 2,
								spaceBetween: 12,
							},
							768: {
								slidesPerView: 3,
								spaceBetween: 20,
							},
							1200: {
								slidesPerView: 4,
								spaceBetween: 20,
							},
						}}
						className="h-full"
					>
						{
							categories?.edges.map(item => {
								return <SwiperSlide key={item.node.id}>
									<div
										className="collection-item relative block cursor-pointer overflow-hidden rounded-2xl !h-full"
										// onClick={() => handleTypeClick("swimwear")}
									>
										<div className="bg-img !h-full">
											<Image src={filterCateImg(item.node.metadata)[0]?.url || "/image/default-image.webp"} width={1000}
														 height={300} alt={item.node.name} className="object-cover w-full h-full" />
										</div>
										{/*<div*/}
										{/*	className="collection-name heading5 bottom-4 w-[100px] rounded-xl  py-1.5 text-center duration-500 sm:bottom-8 md:w-[160px] md:py-3 lg:w-[200px] bg-black text-white">*/}
											{locale===defaultLocale ? item.node.name :item.node.translation ? item.node.translation.name :item.node.name}
										{/*</div>*/}
										{/*<div className="w-full">*/}
										{/*	<h3 className="text-2xl max-md:text-xl font-bold">	{locale===defaultLocale ? item.node.name :item.node.translation ? item.node.translation.name :item.node.name}</h3>*/}

										{/*</div>*/}
									</div>
								</SwiperSlide>;
							})
						}


					</Swiper>
				</div>
			</div>
		</>
	);
};

export default Collection;
