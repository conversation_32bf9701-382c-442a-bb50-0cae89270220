"use client";
import { useEffect, useState } from "react";
import { BodyText } from "@/components/BodyText";
import Modals from "@/components/Modals";
import { RingLoader } from "react-spinners";
import CenterLayout from "@/components/Layout/center-layout";
import { useLocale, useTranslations } from "next-intl";
import {
	AddressDefault,
	AddressiDel,
	Addressiupdate,
	GETAddresscountryArea,
	getAddresslist,
	userAccountAddressCreate,
} from "@/lib/api/user";
import { parsePhoneNumber, isValidPhoneNumber } from "libphonenumber-js";
import { Select, App } from "antd";
import { Country, State, City } from "country-state-city";

import { Controller, useForm } from "react-hook-form";
import clsx from "clsx";
import PhoneInput from "react-phone-input-2";
import { useCountry } from "@/lib/hooks/useCountry";
import { useUserStore } from "@/store/user.store";
import axios from "axios";
import { header, shippingTitle } from "@/lib/utils/util";

const { Option } = Select;
const billingTitle = "center.920bb446dde5de43c1e85fc07547a58caa5e";

export default function Center() {
	// 将 message 替换为 App.useApp()
	const { message } = App.useApp();
	const {
		register,
		handleSubmit,
		setValue,
		reset,
		formState: { errors, isValid },
		getValues: getFormValues,
		control,
	} = useForm({
		mode: "onBlur",
	});
	const [loading, setLoading] = useState(false);
	const [shipping, setShipping] = useState<any>([]);
	const [open, setOpen] = useState(false);

	let { userInfo } = useUserStore();
	const [userShipping, setUserShipping] = useState();

	const [state, setState] = useState<any>(null);

	const t = useTranslations();
	const locale = useLocale();
	// 1. 初始化时只获取一次国家列表
	const [countries, setcountries] = useState([]);
	const [country, setCountry] = useState(Country.getAllCountries());

	//省
	const [countryArea, setcountryArea] = useState([]);

	//是否是编辑
	const [isEdit, setisEdit] = useState(false);
	//编辑 数据id
	const [Editid, setEditid] = useState("");
	// 1. 新增 state 保存待选中的 area
	const [pendingArea, setPendingArea] = useState("");
	useEffect(() => {
		getdata();
	}, []);

	//获取数据列表
	async function getdata() {
		let data = await getAddresslist(userInfo.token);
		if (data.me.addresses) {
			// 设置地址信息
			setShipping(data.me.addresses);
		}
	}

	// 编辑地址
	const handleEdit = async (item: any) => {
		setOpen(true);
		setisEdit(true);
		setEditid(item.id);
		const phoneNumber = item.phone.startsWith("+") ? item.phone.substring(1) : item.phone;
		setValue("phone", phoneNumber);

		// 只在需要时获取州/省列表
		const { addressValidationRules } = await GETAddresscountryArea(item.country.code, userInfo.token);
		setcountryArea(addressValidationRules.countryAreaChoices);
		setPendingArea(item.countryArea); // 保存待选中的 area

		setValue("firstName", item.firstName);
		setValue("lastName", item.lastName);
		setValue("companyName", item.companyName);
		setValue("country", item.country.code);
		setValue("city", item.city);
		setValue("streetAddress1", item.streetAddress1);
		setValue("postalCode", item.postalCode);
	};
	// 3. 用 useEffect 监听 countryArea 变化
	useEffect(() => {
		if (pendingArea && countryArea.length > 0) {
			// 这里 countryArea 是 options 列表
			const matchedArea = countryArea.find(
				(area) => area.raw === pendingArea || area.verbose === pendingArea,
			);
			setValue("countryArea", matchedArea ? matchedArea.raw : "");
			setPendingArea(""); // 只执行一次
		}
	}, [countryArea, pendingArea, setValue]);
	// 设为默认地址
	// 修改设置默认地址的函数
	const handleSetDefault = async (item: any) => {
		try {
			// 显示加载状态
			message.loading({ content: t("common.processing"), key: "setDefault" });

			// 构建请求参数
			const params = {
				addressId: item.id,
				type: "SHIPPING", // 指定地址类型为配送地址
			};

			// 调用设置默认地址的 API
			const response = await AddressDefault(params, userInfo.token);

			console.log(response, "response");

			if (response.accountSetDefaultAddress?.errors?.length === 0) {
				// 更新成功
				message.success({
					content: t("common.setDefaultSuccess"),
					key: "setDefault",
				});
				// 刷新地址列表
				getdata();
			} else {
				// 更新失败
				message.error({
					content: response.accountSetDefaultAddress?.errors[0]?.message || t("common.setDefaultFailed"),
					key: "setDefault",
				});
			}
		} catch (error) {
			console.error("Set default address error:", error);
			message.error({
				content: t("common.errorOccurred"),
				key: "setDefault",
			});
		}
	};

	//提交 创建or修改
	const onsubmit = async (data: any) => {
		let Formdata = { ...getFormValues() };
		Formdata.phone = `+${data.phone}`;

		if (isEdit) {
			//修改地址
			Formdata.id = Editid;

			try {
				setLoading(true);
				// 创建地址
				let { accountAddressUpdate } = await Addressiupdate(Formdata, userInfo.token);
				console.log(accountAddressUpdate, 2);

				if (accountAddressUpdate.errors.length > 0) {
					message.error(accountAddressUpdate.errors[0].field + "-" + accountAddressUpdate.errors[0].message);
				} else {
					// 刷新地址列表
					getdata();
					setOpen(false);
					message.success(t("common.Updated successfully"));
				}
			} catch (e) {
				console.log(e);
			} finally {
				setLoading(false);
			}
		} else {
			try {
				setLoading(true);
				// 创建地址
				let { accountAddressCreate } = await userAccountAddressCreate(Formdata, userInfo.token);
				console.log(accountAddressCreate, 99);

				if (accountAddressCreate.errors.length > 0) {
					message.error(accountAddressCreate.errors[0].field + "-" + accountAddressCreate.errors[0].message);
				} else {
					if (shipping.length == 0) {
						const params = {
							addressId: accountAddressCreate.address.id,
							type: "SHIPPING",
						};
						const response = await AddressDefault(params, userInfo.token);
					}
					// 刷新地址列表
					getdata();
					setOpen(false);
					message.success(t("common.Created successfully"));
				}
			} catch (e) {
				console.log(e);
			} finally {
				setLoading(false);
			}
		}
	};
	// 选中地区
	// 2. 简化 handleChangeCountry 函数
	const handleChangeCountry = async (e: React.ChangeEvent<HTMLSelectElement>) => {
		const selectedCountry = e.target.value;
		console.log(selectedCountry, "selectedCountry");
		setValue("country", selectedCountry);

		// 只在需要时获取州/省列表
		const { addressValidationRules } = await GETAddresscountryArea(selectedCountry, userInfo.token);

		setcountryArea(addressValidationRules.countryAreaChoices);
	};

	const del = async () => {
		let { accountAddressDelete } = await AddressiDel(Editid, userInfo.token);
		if (accountAddressDelete.errors.length > 0) {
			message.error(accountAddressDelete.errors[0].field + "-" + accountAddressDelete.errors[0].message);
		} else {
			getdata();
			setOpen(false);
			message.success(t("common.Created successfully"));
		}
	};

	//获取国家
	async function getCountries() {
		let { data } = await axios.post(
			`${process.env.NEXT_PUBLIC_AI_URL}/api/v1/ml_web/get_translate_country_json?language_code=${locale}`,
			{
				header,
			},
		);
		if (data.code == 200) {
			setcountries(data.result.country);
		}

		// console.log(data.result.country);
	}

	useEffect(() => {
		getCountries();
	}, []);
	return (
		<CenterLayout>
			<div className="grid grid-cols-1 gap-5 py-10 max-md:h-auto max-md:overflow-y-auto md:max-h-[1000px] md:grid-cols-2 md:gap-6 md:overflow-y-scroll lg:gap-7">
				{shipping &&
					shipping.map((item) => (
						<div key={item.id} className="list relative rounded-2xl border border-themeSecondary200 p-7">
							{/* 默认地址标识 */}
							{item.isDefaultShippingAddress && (
								<div className="absolute -left-1 -top-1 flex items-center gap-1 rounded-br rounded-tl bg-main px-2 py-1">
									<svg
										xmlns="http://www.w3.org/2000/svg"
										className="h-4 w-4 text-white"
										viewBox="0 0 20 20"
										fill="currentColor"
									>
										<path
											fillRule="evenodd"
											d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z"
											clipRule="evenodd"
										/>
									</svg>
									<span className="text-xs text-white">{t("common.default")}</span>
								</div>
							)}
							<div className="b-flex">
								<h3 className="font-bold">{t(shippingTitle)}</h3>
								<span
									className="cursor-pointer text-sm text-themeSecondary400 hover:text-main"
									onClick={() => {
										handleEdit(item);
									}}
								>
									{t("common.Edit")}
								</span>
							</div>
							<BodyText size="md" intent="medium" className="mt-4 text-themeSecondary600">
								{`${item?.firstName} ${item?.lastName}`}
							</BodyText>
							<BodyText size="sm" className="mt-2 text-themeSecondary500">
								{`${item?.streetAddress1}`} <br />
								{`${item?.city}, ${item?.countryArea}, ${item?.postalCode}`} {`${item?.country.country}`}
							</BodyText>
							<BodyText size="sm" className="mt-2 text-themeSecondary600">
								{`${item?.phone}`}
							</BodyText>

							{/* 设为默认地址按钮 */}
							{!item.isDefaultShippingAddress && (
								<div className="mt-4 flex justify-end">
									<button
										onClick={() => handleSetDefault(item)}
										className="flex items-center gap-1 text-sm text-themeSecondary600 transition-colors duration-200 hover:text-main"
									>
										<svg
											xmlns="http://www.w3.org/2000/svg"
											className="h-4 w-4"
											fill="none"
											viewBox="0 0 24 24"
											stroke="currentColor"
										>
											<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
										</svg>
										{t("common.setAsDefault")}
									</button>
								</div>
							)}
						</div>
					))}

				{/* 添加地址 */}
				<div
					onClick={() => {
						// 填充表单数据
						setValue("firstName", "");
						setValue("lastName", "");
						setValue("companyName", "");
						setValue("country", "");
						setValue("countryArea", "");
						setValue("city", "");
						setValue("streetAddress1", "");
						setValue("postalCode", "");

						setValue("phone", "");
						setOpen(true);
						setisEdit(false);
					}}
					className="list rounded-2xl border border-themeSecondary200 p-7"
				>
					<div className="flex h-full w-full cursor-pointer flex-col items-center justify-center">
						{/* 添加图标 */}
						<div className="mb-4 flex h-12 w-12 items-center justify-center rounded-full border-2 border-dashed border-themeSecondary400">
							<svg
								xmlns="http://www.w3.org/2000/svg"
								className="h-6 w-6 text-themeSecondary400"
								fill="none"
								viewBox="0 0 24 24"
								stroke="currentColor"
							>
								<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
							</svg>
						</div>

						{/* 添加文字 */}
						<h3 className="text-center text-base font-medium text-themeSecondary400">
							{t("common.addNewAddress")}
						</h3>
						<p className="mt-2 text-center text-sm text-themeSecondary400">{t("common.clickToAdd")}</p>
					</div>
				</div>
			</div>
			<Modals open={open} setOpen={setOpen} top="20vh">
				<div className="s-flex  mb-5">
					<h3 className="text-xl font-bold !text-black">{t(shippingTitle)}</h3>
				</div>
				<div className="z-50 w-[90vw] lg:w-[80vw] xl:w-[50vw]">
					<form onSubmit={handleSubmit(onsubmit)}>
						<div className={clsx("w-full gap-y-8")}>
							{/* First Name & Last Name */}
							<div className="grid gap-8 sm:flex sm:gap-5">
								{/* First Name */}
								<div className="mb-5 w-full sm:w-1/2">
									<div className="relative">
										<label
											className={`absolute -top-2 ${
												errors.firstName ? "text-red-400" : "text-[#85929E]"
											} left-3 bg-white text-xs`}
											htmlFor="firstName"
										>
											{t("form.f788c9b20398a5481f38c08cf68f2cb8c964")}
										</label>
										<input
											className={`w-full appearance-none rounded-md border px-5 py-3.5 ${
												errors.firstName ? "border-red-400" : "border-[#DDE6F5]"
											} focus:shadow-outline leading-tight text-gray-700 focus:shadow-lg focus:outline-none`}
											type="text"
											placeholder={t("form.f788c9b20398a5481f38c08cf68f2cb8c964")}
											id="firstName"
											{...register("firstName", {
												required: t("message.f5414f2236ca1a42db98da7c6b796fbbf035"),
											})}
										/>
									</div>
								</div>
								{/* Last Name */}
								<div className="mb-5 w-full  sm:w-1/2">
									<div className="relative">
										<label
											className={`absolute -top-2 ${
												errors.lastName ? "text-red-400" : "text-[#85929E]"
											} left-3 bg-white text-xs`}
											htmlFor="lastName"
										>
											{t("form.2bb382a16eab9d446ac91af90ce99c3cc7f8")}
										</label>
										<input
											className={`focus:shadow-outline w-full appearance-none rounded-md border px-5 py-3.5 leading-tight text-gray-700 focus:shadow-lg focus:outline-none ${
												errors.lastName ? "border-red-400" : "border-[#DDE6F5]"
											}`}
											type="text"
											placeholder={t("form.2bb382a16eab9d446ac91af90ce99c3cc7f8")}
											id="lastName"
											{...register("lastName", {
												required: t("message.8fb4bc206b4dd947543999d66757d14712e6"),
											})}
										/>
									</div>
								</div>
							</div>

							{/* companyName name & country */}
							<div className="mb-5 grid gap-8 sm:flex  sm:gap-5">
								{/* 国家 */}
								{/* Country  */}
								<div className="w-full sm:w-1/2">
									<div className="relative">
										<label
											className={`absolute -top-2 ${
												errors.country ? "text-red-400" : "text-[#85929E]"
											} left-3 bg-white text-xs`}
											htmlFor="country"
										>
											{t("checkout.eae6597b15706c40889b12521a6e903edff3")}
										</label>
										<select
											title={t("form.country")}
											className={`focus:shadow-outline w-full appearance-none rounded-md border px-5 py-3.5 leading-tight text-gray-700 focus:shadow-lg focus:outline-none ${
												errors.country ? "border-red-400" : "border-[#DDE6F5]"
											}`}
											{...register("country")}
											onChange={handleChangeCountry}
										>
											<option value="">{t("checkout.861db9266e3bf743b5c84bb5b8de3b67ebcc")}</option>
											{countries.map((item, index) => (
												<option key={index} value={item.isoCode}>
													{item.isoCode} {item.name}
												</option>
											))}
										</select>
									</div>
								</div>

								{/*省 countryArea */}

								<div className="w-full sm:w-1/2">
									<div className="relative">
										<label
											className={`absolute -top-2 z-[1] ${
												errors.countryArea ? "text-red-400" : "text-[#85929E]"
											} left-3 bg-white text-xs`}
											htmlFor="countryArea"
										>
											{t("checkout.1350af9875f65e46975b46c51798e07e4ec6")}
										</label>
										<select
											title={t("form.countryArea")}
											className={`focus:shadow-outline w-full appearance-none rounded-md border px-5 py-3.5 leading-tight text-gray-700 focus:shadow-lg focus:outline-none ${
												errors.country ? "border-red-400" : "border-[#DDE6F5]"
											}`}
											id="countryArea"
											{...register("countryArea")}
											// onChange={handleChangeCountry}
										>
											<option value="">{t("checkout.dc09d2d018ce2d462a3837538323e8b86ad8")}</option>
											{countryArea?.map((item, index) => (
												<option key={index} value={item.raw}>
													{item.verbose}
												</option>
											))}
										</select>
									</div>
								</div>
							</div>

							{/* Town / City & District */}
							<div className="mb-5 grid gap-8 sm:flex  sm:gap-5">
								{/* Town / City */}
								<div className="w-full sm:w-1/2">
									<div className="relative">
										<label
											className={`absolute -top-2 ${
												errors.city ? "text-red-400" : "text-[#85929E]"
											} left-3 bg-white text-xs`}
											htmlFor="city"
										>
											{t("checkout.db41a8b095d4e045dec8e41ee7b29f79a6e7")}
										</label>
										<input
											className={`focus:shadow-outline w-full appearance-none rounded-md border px-5 py-3.5 leading-tight text-gray-700 focus:shadow-lg focus:outline-none ${
												errors.city ? "border-red-400" : "border-[#DDE6F5]"
											}`}
											type="text"
											placeholder={t("form.City")}
											id="city"
											{...register("city", {
												required: t("message.cc6e29b38f06b64001e8e9a6a1bf81a2d3ef"),
											})}
										/>
									</div>
								</div>

								{/* Company name */}
								<div className="w-full sm:w-1/2">
									<div className="relative">
										<label
											className="absolute -top-2 left-3 bg-white text-xs text-[#85929E]"
											htmlFor="companyName"
										>
											{t("checkout.50d8b5d2a6636b442d08250ad47532b73fd5")}
										</label>
										<input
											className="focus:shadow-outline w-full appearance-none rounded-md border border-[#DDE6F5] px-5 py-3.5 leading-tight text-gray-700 focus:shadow-lg focus:outline-none"
											type="text"
											placeholder={t("form.c0b9782f9dec7d44b00be190c883b25e35f0")}
											id="companyName"
											{...register("companyName")}
										/>
									</div>
								</div>
							</div>
							{/* Street address */}
							<div className="mb-5 grid gap-8 sm:flex  sm:gap-5">
								<div className="w-full">
									<div className="relative">
										<label
											className={`absolute -top-2 ${
												errors.streetAddress1 ? "text-red-400" : "text-[#85929E]"
											} left-3 bg-white text-xs`}
											htmlFor="streetAddress1"
										>
											{t("checkout.7db196092b824b40ca89655c15feadaf064e")}
										</label>
										<input
											className={`focus:shadow-outline w-full appearance-none rounded-md border px-5 py-3.5 leading-tight text-gray-700 focus:shadow-lg focus:outline-none ${
												errors.streetAddress1 ? "border-red-400" : "border-[#DDE6F5]"
											}`}
											type="text"
											placeholder={t("common.Address")}
											id="streetAddress1"
											{...register("streetAddress1", {
												required: t("message.c9183efb9d6f184d03390158cd782f83a406"),
											})}
										/>
									</div>
								</div>
							</div>
							{/* Postcode / ZIP & Phone */}
							<div className="grid gap-8 sm:flex sm:gap-5">
								<div className="w-full sm:w-1/2">
									<div className="relative">
										<label
											className={`absolute -top-2 ${
												errors.postalCode ? "text-red-400" : "text-[#85929E]"
											} left-3 bg-white text-xs`}
											htmlFor="postalCode"
										>
											{t("checkout.dc9ed9c83a3614477c18c92b64ea25203d52")}
										</label>
										<input
											className={`focus:shadow-outline w-full appearance-none rounded-md border px-5 py-3.5 leading-tight text-gray-700 focus:shadow-lg focus:outline-none ${
												errors.postalCode ? "border-red-400" : "border-[#DDE6F5]"
											}`}
											type="text"
											placeholder={t("checkout.postalCode")}
											id="postalCode"
											{...register("postalCode", {
												required: t("message.3b70d452b0ded6486e2a9f82f3db3aac0e75"),
											})}
										/>
									</div>
								</div>

								{/* Phone */}
								<div className="w-full sm:w-1/2">
									<div className="relative">
										<label
											className={`absolute -top-2 ${
												errors.phone ? "text-red-400" : "text-[#85929E]"
											} left-3 z-10 bg-white text-xs`}
											htmlFor="phone"
										>
											{t("common.Phone")}
										</label>
										<Controller
											name="phone"
											control={control}
											rules={{
												required: t("message.6da2af7b8d5c5345f20988a3cbccd7cdfb8f"),
												validate: (value) => {
													try {
														// 如果电话号码不包含 '+' 号，添加它
														const phoneNumberWithPlus = `+${value}`;

														// 验证电话号码是否有效
														if (!isValidPhoneNumber(phoneNumberWithPlus)) {
															return t("common.invalidPhone");
														}

														// 获取电话号码的详细信息
														const phoneNumber = parsePhoneNumber(phoneNumberWithPlus);

														// 检查是否是可能的号码
														if (!phoneNumber.isPossible()) {
															return t("message.invalidPhoneLength");
														}

														return true;
													} catch (error) {
														return t("common.invalidPhone");
													}
												},
											}}
											render={({ field: { onChange, value } }) => (
												<PhoneInput
													country={"us"}
													enableSearch
													searchPlaceholder={t("common.searchCountry")}
													inputProps={{
														id: "phone",
														placeholder: t("checkout.78fb2d8b706a0d46bfb800f2d2c97e103e1e"),
													}}
													value={value}
													onChange={(phone, country: any) => {
														onChange(phone);
														// 如果需要，可以更新国家信息
														setUserShipping((prev: any) => ({
															...prev,
															countryCode: country.countryCode,
															dialCode: country.dialCode,
														}));
													}}
													containerClass={`${errors.phone ? "phone-input-error" : ""}`}
													inputStyle={{
														width: "100%",
														height: "48px", // 匹配原来的高度
														borderRadius: "0.375rem",
														borderColor: errors.phone ? "#f87171" : "#DDE6F5",
														backgroundColor: "white",
													}}
													buttonStyle={{
														borderRadius: "0.375rem 0 0 0.375rem",
														borderColor: errors.phone ? "#f87171" : "#DDE6F5",
														backgroundColor: "white",
													}}
													searchStyle={{
														width: "90%",
														margin: "0 auto",
													}}
													dropdownStyle={{
														width: "350px",
													}}
												/>
											)}
										/>
										{errors.phone && (
											<p className="mt-1 text-xs text-red-400">
												{t("message.6da2af7b8d5c5345f20988a3cbccd7cdfb8f")}
											</p>
										)}
									</div>
								</div>
							</div>
						</div>

						<div className="flex gap-3 max-md:mb-[60px]">
							{/* Order Button */}
							<button
								id="buy-button"
								type="submit"
								className={`${
									loading
										? "cursor-not-allowed bg-gray-800 text-white shadow-md"
										: "shadow-4xl cursor-pointer bg-main text-white hover:opacity-70"
								} font-Roboto mt-6 flex items-center justify-center gap-4 rounded-md px-7 py-4 text-base font-semibold capitalize transition-all duration-300 ease-in-out`}
								disabled={loading}
							>
								{loading ? t("order.Processing") + "..." : t("form.89d6993324b64541c93976dbb5de7af086c0")}

								{loading && <RingLoader color="#fff" size={20} />}
							</button>

							{/* 删除按钮 */}
							{isEdit && (
								<button
									type="button"
									onClick={() => del()}
									className={`mt-6 flex items-center justify-center gap-2 rounded-md bg-red-500 px-7 
  py-4 text-white transition-colors duration-300 hover:bg-red-600
  ${loading ? "cursor-not-allowed opacity-70" : "cursor-pointer"}`}
								>
									{/* 删除图标 */}
									<svg
										xmlns="http://www.w3.org/2000/svg"
										className="h-5 w-5"
										viewBox="0 0 20 20"
										fill="currentColor"
									>
										<path
											fillRule="evenodd"
											d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z"
											clipRule="evenodd"
										/>
									</svg>

									{/* 按钮文字 */}
									{loading ? t("common.deleting") : t("common.deleteAddress")}

									{/* 加载动画 */}
									{loading && <RingLoader color="#fff" size={20} />}
								</button>
							)}
						</div>
					</form>
				</div>
			</Modals>
		</CenterLayout>
	);
}
