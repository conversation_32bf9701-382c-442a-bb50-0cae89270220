'use client'
import { Placeholder } from "@/components/Placeholder";
import { BodyText } from "@/components/BodyText";
import React from "react";
import { useTranslations } from "next-intl";

type Props = {
  text?: string;
  className?: string;
  children?: React.ReactNode;
}
export default function EmptyState({ text = "", className = "", children }: Props) {
  const t = useTranslations();
  const context = text || t("common.NO_DATA");
  return <div className={`c-flex flex-col my-6 ${className} select-none`}>
    <Placeholder src="/image/empty-state.svg" imageWidth={400} imageHeight={400} quality={50}
                 alt={context}></Placeholder>
    <BodyText size={"xl"} className="text-gray-500">
      {context}
    </BodyText>
    {children}
  </div>;
  // <>
  //    <div className="text-center w-full h-full">
  //       <div className="mb-6">
  //           <i className="far fa-heart text-gray-300 text-6xl"></i>
  //       </div>
        
        
  //       <p className="text-gray-600 mb-8 max-w-lg">
  //           You don't have any products in the wishlist yet. You will find a lot of interesting products on our "Shop page".
  //       </p>
        
  //       <a href="#" className="inline-block border border-gray-300 px-8 py-3 text-black hover:bg-gray-50 transition-colors">
  //           Return to shop
  //       </a>
  //   </div>
  // </>
}
