mutation UserRegister($email: String!, $password: String!, $channel: String!, $locale: LanguageCodeEnum!, $firstName: String, $lastName: String, $metadata: [MetadataInput!]) {
	accountRegister(input: {
		email: $email,
		password: $password,
		channel: $channel,
		languageCode: $locale,
		firstName: $firstName,
		lastName: $lastName,
		metadata: $metadata
	}) {
		user {
			email
			firstName
			lastName
			metadata {
				key
				value
			}
		}
		errors {
			field
			message
			code
		}
	}
}
