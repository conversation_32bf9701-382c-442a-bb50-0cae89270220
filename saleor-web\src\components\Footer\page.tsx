"use client";
import React, { useEffect, useState } from "react";
import { Input, Button, message } from "antd";
import Image from "next/image";
import { Link } from "@/navigation";
import { <PERSON>edin<PERSON>ogo, TiktokLogo } from "@phosphor-icons/react";
import { useCategory } from "@/context/CategoriesContext";
import FollowUs from "@/components/Social/FollowUs";
import { useLocale, useTranslations } from "next-intl";
const { Search } = Input;
import { Menu, menus } from "@/lib/menu";
import { contactInfo, payarr } from "@/lib/contacts";
import Follow from "../Social/Follow";
import { useModalCustomerContext } from "@/context/CustomerContext";
import SliderBar from "../SliderBar";
import { subscribeEmail } from "@/lib/api/subscribe";
import { defaultLocale } from "@/config";
import clsx from "clsx";
import Collapse from "../Collapse";
import Indicator from "../Indicator";
import useIsMobile from "@/lib/hooks/useIsMobile";

const Footer = ({ categories }) => {


	let locale = useLocale();
	//是否展开
	const [isOpen, setIsOpen] = useState({
		isQuickIinks: true,
		isProducts: true,
		isEmail: true,
	});
	const t = useTranslations();
	const [subscribeLoading, setSubscribeLoading] = useState(false);

	let isMobile = useIsMobile()
	useEffect(() => {
		if (isMobile) {
			setIsOpen({
				isQuickIinks: false,
				isProducts: false,
				isEmail: false,
			})
		} else {
			setIsOpen({
				isQuickIinks: true,
				isProducts: true,
				isEmail: true,
			})
		}

	}, [isMobile]);
	const subscribe = async (value: string) => {
		try {
			if (!value) return;
			setSubscribeLoading(true);
			await subscribeEmail({ email: value, subscribe_type: 3 });
			message.success(t("message.subscribeSuccess"));
		} catch (e) {
			message.error(e.message);
		}
		setSubscribeLoading(false);
	};

	const navigation = {
		QUICKLINKS: [
			{ id: 1, name: "menu.home", link: "/", hasSlug: true, show: true },
			{ id: 20, name: "menu.product", link: "/products", hasSlug: true, show: true },
			{ id: 3, name: "menu.blog", link: "/blog", hasSlug: true, show: true },
			{ id: 4, name: "menu.aboutUs", link: "/about-us", hasSlug: true, show: true },
			{ id: 5, name: "menu.contactUs", link: "/contact-us", hasSlug: true, show: true },
			// { id: 6, name: "menu.faqs", link: "/faqs", hasSlug: true, show: true },
		],
		contact: [
			{
				icon: <i className="ri-building-line"></i>,
				name: t(`common.company`),
				href: `javascript:void(0)`,
				value: `${t(`common.company_name`)}`,
			},
			{
				icon: <i className="ri-map-pin-line"></i>,
				name: t(`menu.Address`),
				href: `https://www.google.com/maps/search/No.+409+Guanbang+International,+Linhe+Road,+Huadu+District,+Guangzhou/@23.4304758,113.1334701,12z/data=!3m1!4b1?entry=ttu&g_ep=EgoyMDI0MTExOS4yIKXMDSoASAFQAw%3D%3D`,
				value: `${contactInfo.address}`,
			},
			// {
			// 	name: ``,
			// 	href: ``,
			// 	value: `${contactInfo.name}`,
			// },
			// {
			// 	name: t(`menu.Phone`),
			// 	href: `tel:${contactInfo.phone}`,
			// 	value: `${contactInfo.phone}`,
			// },

			// {
			// 	name: t(`menu.Emall`),
			// 	href: `mailto:${contactInfo.email}`,
			// 	value: `${contactInfo.email}`,
			// },
		],
	};


	return (
		<footer className="footer bg-[#f5f5f7] text-black max-md:pb-[70px] relative">
			<div className="h-[1px] absolute top-[160px] bg-gray-200 w-[100%] max-w-[1640px] left-[50%] -translate-x-1/2 max-lg:hidden"></div>
			<div className="container  grid grid-cols-4 gap-5 px-4 pt-[120px] pb-[100px] text-[14px] max-lg:grid-cols-2 max-md:grid-cols-1 max-md:py-[33px] ">
				{/* 第一部分 */}
				<div className="2xl:pr-[50px]">
					<Image
						src="/image/logo.png"
						width={500}
						height={500}
						alt={`${process.env.NEXT_PUBLIC_COMPANY_NAME} logo`}
						className="mb-12 object-contain w-[120px]"
					></Image>
					<ul className="space-y-1 mb-10">
						{navigation.contact.map((item, index) => {
							return (
								<li key={item.name}>
									<Link href={item.href} className="  text-[#707070] hover:text-black text-[12px]">
										<span className="mr-2 text-lg">{item.icon}</span>
										{/* <span className="text-[12px]">{item.name}</span> */}
										{/* <span className={clsx(!item.name && "hidden")}>:</span>{" "} */}
										<span>{item.value}</span>
									</Link>
								</li>
							);
						})}
					</ul>
					{/* <Indicator isHovercolor={true} color="#a1a1a1" msg={t("nav.Get direction")} className="text-[#a1a1a1]" /> */}
					<Follow />
				</div>

				{/* 第二部分 */}
				<div>
					<div className="max-md:flex max-md:justify-between">
						<h3 className=" mb-10 text-[14px] max-md:mb-3 text-[#282828]  ">{t("menu.product")}</h3>
						<i className={clsx("hidden max-md:block text-xl", !isOpen.isProducts ? "ri-add-line" : "ri-subtract-fill")} onClick={() => setIsOpen(prevState => ({ ...prevState, isProducts: !prevState.isProducts }))}></i>
					</div>

					<Collapse open={isOpen.isProducts}>
						<ul className="space-y-2">
							{categories?.edges?.map((item) => {
								return (
									<li key={item.node.id}>
										<Link href={"/products/" + item.node.slug} className="text-[#707070] hover:text-black text-[12px] font-normal">
											{locale == defaultLocale ? item.node.name : (item.node?.translation?.name || "") || item.node.name}
										</Link>
									</li>
								);
							})}
						</ul>
					</Collapse>


				</div>
				{/* 第三部分 */}
				<div>
					<div className="max-md:flex max-md:justify-between">
						<h3 className=" mb-10 text-[14px] max-md:mb-3  text-[#282828]">{t("menu.Quick Iinks")}</h3>
						<i className={clsx("hidden max-md:block text-xl", !isOpen.isQuickIinks ? "ri-add-line" : "ri-subtract-fill")} onClick={() => setIsOpen(prevState => ({ ...prevState, isQuickIinks: !prevState.isQuickIinks }))}></i>
					</div>

					<Collapse open={isOpen.isQuickIinks}>
						<ul className="space-y-2">
							{navigation.QUICKLINKS.map((item) => {
								return (
									item.show && (
										<li key={item.id}>
											<Link href={item.link} className="  text-[#707070] hover:text-black text-[12px] font-normal">
												{t(item.name)}
											</Link>
										</li>
									)
								);
							})}
						</ul>
					</Collapse>

				</div>
				{/* 第四部分 */}
				<div className="">
					<div className="max-md:flex max-md:justify-between">
						<h3 className=" mb-10 text-[14px] max-md:mb-3  text-[#282828]">{t("menu.Subscribe here")}</h3>
						<i className={clsx("hidden max-md:block text-xl", !isOpen.isEmail ? "ri-add-line" : "ri-subtract-fill")} onClick={() => setIsOpen(prevState => ({ ...prevState, isEmail: !prevState.isEmail }))}></i>
					</div>
					<Collapse open={isOpen.isEmail}>
						<p className="text-[12px]  text-[#707070] mb-5">{t("menu.info")}</p>
						<SearchInput onSearch={subscribe} />
					</Collapse>

				</div>

			</div>

			<div className="container">
				<div className="  flex items-center justify-center max-md:flex-col max-md:justify-center max-md:gap-y-5 border-t border-gray-200 py-4 text-[14px] text-[#707070]">
					<span className="text-[#b4b4b4]">{t("footer.copyright")}</span>
					{/* <span className="text-lg text-white flex gap-1" >
						{
							payarr.map(item => (<img key={item.img} src={item.img} className="w-[50px] h-[30px]" alt={item.alt} />))
						}
					</span> */}
				</div>
			</div>
		</footer>
	);
};

export default Footer;


function SearchInput({ onSearch, className }: any) {
	const [searchValue, setSearchValue] = useState("");
	const t = useTranslations();
	const [subscribeLoading, setSubscribeLoading] = useState(false);

	const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
		if (e.key === "Enter") {
			handleSearch(e.currentTarget.value);
		}
	};

	const handleSearch = async (value: string) => {
		if (!value) {
			message.error(t("message.921f2b497830d143aacbf3ab15ec70888bce"));
			return;
		}
		setSubscribeLoading(true);
		await onSearch(value);
		setSubscribeLoading(false);
	};

	const toSearch = () => {
		handleSearch(searchValue);
	};

	const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		setSearchValue(e.target.value);
	};

	return (
		<div className="w-full">
			<div className={"flex flex-col gap-4"}>
				<input
					value={searchValue}
					onChange={handleChange}
					onKeyDown={handleKeyDown}
					placeholder={t("nav.Enter")}
					autoComplete="off"
					disabled={subscribeLoading}
					className="w-full rounded-lg bg-white px-4 py-2.5 text-gray-800 max-w-[75%] ml-[5px] outline-none focus:outline-1 focus:outline-gray-300 ring-1 ring-black/20 transition-all placeholder:text-gray-400 focus:ring-2 focus:ring-black disabled:cursor-not-allowed disabled:opacity-50 text-sm"
				/>
				<button
					onClick={toSearch}
					type="submit"
					disabled={subscribeLoading}
					className="group relative w-fit text-[#333333] transition-colors disabled:cursor-not-allowed disabled:opacity-50"
				>
					<span className="inline-flex border-b items-center text-sm font-medium hover:border-[#333333]">
						{subscribeLoading ? (
							<>
								<svg className="mr-2 h-4 w-4 animate-spin" viewBox="0 0 24 24">
									<circle
										className="opacity-25"
										cx="12"
										cy="12"
										r="10"
										stroke="currentColor"
										strokeWidth="4"
										fill="none"
									/>
									<path
										className="opacity-75"
										fill="currentColor"
										d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
									/>
								</svg>
								{t("loading")}
							</>
						) : (
							<>
								{t("menu.Subscribe here")}
								<svg
									className="ml-1 h-4 w-4 transform transition-transform group-hover:translate-x-1"
									fill="none"
									viewBox="0 0 24 24"
									stroke="currentColor"
								>
									<path
										strokeLinecap="round"
										strokeLinejoin="round"
										strokeWidth={2}
										d="M17 8l4 4m0 0l-4 4m4-4H3"
									/>
								</svg>
							</>
						)}
					</span>
					<span className="absolute -bottom-0.5 left-0 h-[1px] w-0 bg-white transition-all group-hover:w-full" />
				</button>
			</div>
		</div>
	);
}
