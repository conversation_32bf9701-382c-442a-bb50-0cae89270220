mutation CreateTransaction(
  $checkoutId: ID!, 
  $amount: PositiveDecimal!,
  $metadata: [MetadataInput!]
  
) {
  transactionCreate(
    id: $checkoutId,
    transaction: {
      name: "Payment transaction",
      message: "Checkout payment",
      pspReference: "paypal",
      availableActions: [CHARGE],
      amountAuthorized: {
        amount: $amount,
        currency: "USD"
      },
      amountCharged: {
        amount: 0,
        currency: "USD"
      },
      amountRefunded: {
        amount: 0,
        currency: "USD"
      },
      amountCanceled: {
        amount: 0,
        currency: "USD"
      },
      metadata: $metadata
    }
  ) {
    transaction {
      id
      name
      message
      metadata {
        key
        value
      }
    }
    errors {
      field
      message
    }
  }
}