fragment ProductListItem on Product {
	id
	...ProductLocaleItem
	name
	slug
	pricing {
		priceRange {
			start {
				gross {
					amount
					currency
				}
			}
			stop {
				gross {
					amount
					currency
				}
			}
		}
	}
    variants {
          id
          name
          channelListings {
            price {
              amount
              currency
            }
            costPrice {
              amount
              currency
            }
          }
          pricing {
            price {
              gross {
                amount
                currency
              }
            }
          }
        }
	descriptionJson
	media: metafield(key: "media")
	metadata {
		key
		value
	}
	translation(languageCode: $locale) {
		descriptionJson
		name
	}
	category {
		id
		...CategoryLocaleItem
		name
	}
	thumbnail(size: 1024, format: WEBP) {
		url
		alt
	}
}
