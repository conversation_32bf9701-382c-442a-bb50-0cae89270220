
import React from 'react'

import { locales } from '@/config';
import { MyPageProps } from '@/lib/@types/base';
import { getBasePageSeo } from '@/lib/api/seo';
import { generateSeo } from '@/lib/utils/seo';
import { Metadata } from 'next';
import dynamic from 'next/dynamic';
import { unstable_setRequestLocale } from 'next-intl/server';


const PaypalState = dynamic(() => import('@/components/Checkout/PaypalState'), {
  ssr: false
 })

export async function generateStaticParams() {
  return locales.map((locale) => ({
    locale,
  }));
} 
/**
 * 生成页面的元数据
 * @param props 页面的属性，用于生成SEO信息
 * @returns 返回页面的元数据，包括SEO信息
 */
export const generateMetadata = async (props: MyPageProps): Promise<Metadata> => {
	props.params.page = ["paypal-state"];
	// 获取基础页面的SEO数据
	const seo = await getBasePageSeo(props);
	// 生成最终的SEO信息，并返回
	return generateSeo(props, {
		...seo,
		ogType: "website",
	});
};
export default function Page(props: MyPageProps) {
  unstable_setRequestLocale(props.params.locale);
  return (
    <div className='container min-h-[90vh]'>
        <PaypalState />
        
      
    </div>
  )
}
