import { cookies } from "next/headers";
import { CheckoutCreateDocument, CheckoutFindDocument } from "@/gql/graphql";
import { executeGraphQL } from "@/lib/utils/graphql";
import { handleGraphqlLocale } from "@/lib/utils/util";

export function getIdFromCookies(channel: string) {
	const cookieName = `checkoutId-${channel}`;
	const checkoutId = cookies().get(cookieName)?.value || "";
	return checkoutId;
}



export async function find(checkoutId: string) {
	try {
		const { checkout } = checkoutId
			? await executeGraphQL(CheckoutFindDocument, {
				variables: {
					id: checkoutId,locale:handleGraphqlLocale('en'),
				},
				cache: "no-cache",
			})
			: { checkout: null };

		return checkout;
	} catch {
		// we ignore invalid ID or checkout not found
	}
}



