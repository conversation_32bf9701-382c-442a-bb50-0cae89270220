"use client";
import clsx from "clsx";
import { useEffect, useState } from "react";
import {

  LoadingOutlined,

} from '@ant-design/icons';
import { useTranslations } from "next-intl";
import { App } from "antd";
type Props = {
	minCount?: number;
	maxCount?: number;
	initCount?: number;
	changeCount?: (count: number) => void;
	className?: string;
	disabled?: boolean;
};
export default function Calculator({
	disabled = false,
	initCount = 1,
	minCount = 1,
	maxCount,
	className = "",
	changeCount,
}: Props) {
  const t = useTranslations();
  const { message } = App.useApp();
	const [count, setCount] = useState(initCount);
	const handleChange = (val: number) => {
		if (disabled) {
			return;
		}
		setCount(() => {
			let value = 0;
			if (val <= minCount) {
				value = minCount;
			} else if (maxCount && val > maxCount) {
        message.error(t("message.Out of stock"));
				value = maxCount;
			} else {
				value = val;
			}
			return +value;
		});
	};
	useEffect(() => {
		changeCount(count);
	}, [count]);
	useEffect(() => {
		setCount(initCount);
	}, [initCount]);
	useEffect(() => {
		setCount(initCount);
	}, [maxCount]);
	return (
		<div
			className={`flex bg-[#f2f2f2] rounded-sm w-fit ${
				disabled ? "bg-gray-100" : ""
			}  h-[50px]  items-center  p-2 sm:mx-0`}
		>
			<button
				disabled={count === 1||disabled}
				onClick={() => handleChange(count - 1)}
        className={clsx("font-arial relative flex cursor-pointer items-center justify-center rounded-md ")}
			>
				<i className="ri-subtract-line ri-lg"></i>
			</button>

{
  !disabled?<input
  className={` font-arial mx-2 text-center !text-[16px] !bg-[#f2f2f2] ${
    disabled ? "bg-[#f2f2f2]" : ""
  } max-w-[100px] border-none text-lg font-normal text-themeSecondary800 focus:outline-none ${className}`}
  type="number"
  value={count}
  onChange={(e: any) => handleChange(e.target.value)}
/>:<div
				className={` font-arial mx-2 text-center !text-[16px] !bg-[#f2f2f2]  w-[100px] border-none text-lg font-normal text-themeSecondary800 focus:outline-none `}

			><LoadingOutlined /></div>
}
			

     

			<button
				onClick={() => handleChange(count + 1)}
        disabled={disabled}
				className={clsx("relative flex cursor-pointer items-center justify-center ")}
			>
				<i className="ri-add-line ri-lg"></i>
			</button>
		</div>
	);
}
