query Orderslist($first: Int, $before: String, $after: String) {
	me {
		orders(after: $after, before: $before, first: $first) {
			totalCount
			edges {
				cursor
				node {
					id
					status
					created
          totalCharged {
            amount
            currency
          }
					checkoutId
					shippingAddress {
						city
						cityArea
						companyName
						countryArea
						firstName
						lastName
						postalCode
						country {
							code
							country
						}
						streetAddress1
						isDefaultBillingAddress
						isDefaultShippingAddress
						id
					}

					lines {
						id
            quantity
						totalPrice {
							gross {
								amount
								currency
							}
							tax {
								amount
								currency
							}
							net {
								amount
								currency
							}
						}
						variant {
							quantityAvailable
							sku
							metadata {
								key
								value
							}
							weight {
								unit
								value
							}
							product {
								id
								media: metafield(key: "media")
								name
								slug
							}
						}
					}
					shippingPrice {
						currency
						gross {
							amount
							currency
						}
						net {
							amount
							currency
						}
						tax {
							amount
							currency
						}
					}
				}
			}
			pageInfo {
				endCursor
				hasNextPage
				hasPreviousPage
				startCursor
			}
		}
	}
}
