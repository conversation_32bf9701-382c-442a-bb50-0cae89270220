"use client";

import { Link } from "@/navigation";
import { useEffect, useState, useCallback, useMemo } from "react";
import { ChevronDown } from "lucide-react";
import { useTranslations } from "next-intl";
import { useParams } from "next/navigation";
import { ProductCategoriesListQuery } from "@/gql/graphql";
import { defaultLocale } from "@/config";

type CategoryEdge = ProductCategoriesListQuery["categories"]["edges"][0];

const CategoryItem = ({
	locale,
	item,
	level = 0,
	currentSlug,
	expandedCategories,
	toggleCategory,
}: {
	locale?: string;
	item: CategoryEdge;
	level?: number;
	currentSlug: string;
	expandedCategories: string[];
	toggleCategory: (id: string) => void;
}) => {
	const hasChildren = item.node.children?.edges?.length > 0;
	const isExpanded = expandedCategories.includes(item.node.id);
	const isActive = item.node.slug === currentSlug;

	return (
		<div className="relative">
			{/* DJI风格的分类项 */}
			<div
				className={`group flex items-center justify-between rounded-full px-4 py-3 mb-2
					transition-all duration-300 cursor-pointer  text-[#000000a6]
					${isActive
						? "bg-[#dfdfdf] text-black"
						: level === 0
							? " text-[#545454] hover:bg-[#e9e9e9]"
							: "text-[#545454] hover:bg-[#e9e9e9]"
					}`}
				style={{
					marginLeft: `${level * 1}rem`,
					fontSize: level === 0 ? '0.95rem' : '0.875rem'
				}}
			>
				<Link
					href={`/products/${item.node.slug}`}
					className={`flex-1 transition-colors duration-200 text-[16px] hover:text-black
						${isActive ? "text-black" : "text-[#545454]"}
						`}
				>
					{locale === defaultLocale ? item.node.name : item.node.translation?.name || item.node.name}
				</Link>

				{hasChildren && (
					<button
						onClick={() => toggleCategory(item.node.id)}
						className={`ml-2 flex h-6 w-6 items-center justify-center rounded-full
							transition-all duration-200
							${isActive ? "hover:bg-white/20" : "hover:bg-white/50"}`}
					>
						<ChevronDown
							className={`h-4 w-4 transform transition-transform duration-200
							${isExpanded ? "rotate-180" : "rotate-0"}
							${isActive ? "text-gray-500" : "text-gray-500"}`}
						/>
					</button>
				)}
			</div>

			{/* 子分类 */}
			{hasChildren && (
				<div
					className={`transition-all duration-300 ease-in-out overflow-hidden
						${isExpanded ? "max-h-96 opacity-100" : "max-h-0 opacity-0"}`}
				>
					<div className="space-y-1">
						{item.node.children?.edges.map((child: CategoryEdge) => (
							<CategoryItem
								key={child.node.id}
								item={child}
								level={level + 1}
								currentSlug={currentSlug}
								expandedCategories={expandedCategories}
								toggleCategory={toggleCategory}
								locale={locale}
							/>
						))}
					</div>
				</div>
			)}
		</div>
	);
};

type CategoriesProps = ProductCategoriesListQuery["categories"];

const findParentCategoryIds = (
	categories: CategoryEdge[] | undefined,
	targetSlug: string,
	parentIds: string[] = [],
): string[] => {
	if (!categories) return [];

	return categories.reduce((acc, category) => {
		if (category.node.slug === targetSlug) return parentIds;

		if (category.node.children?.edges?.length) {
			const result = findParentCategoryIds(category.node.children.edges, targetSlug, [
				...parentIds,
				category.node.id,
			]);
			if (result.length) return result;
		}
		return acc;
	}, [] as string[]);
};

const getSiblingCategories = (categories: CategoryEdge[] | undefined, targetId: string): string[] => {
	if (!categories) return [];

	const findParent = (
		items: CategoryEdge[],
		id: string,
	): { parent: CategoryEdge | null; siblings: string[] } => {
		for (const item of items) {
			// 如果是顶级类别
			if (item.node.id === id) {
				return {
					parent: null,
					siblings: items.map((cat) => cat.node.id).filter((catId) => catId !== id),
				};
			}

			// 在子类别中查找
			if (item.node.children?.edges) {
				for (const child of item.node.children.edges) {
					if (child.node.id === id) {
						return {
							parent: item,
							siblings: item.node.children.edges.map((cat) => cat.node.id).filter((catId) => catId !== id),
						};
					}
				}
				const result = findParent(item.node.children.edges, id);
				if (result.parent || result.siblings.length) return result;
			}
		}
		return { parent: null, siblings: [] };
	};

	return findParent(categories, targetId).siblings;
};

// 二级分类项组件
const SubCategoryItem = ({
	locale,
	item,
	currentSlug,
}: {
	locale?: string;
	item: CategoryEdge;
	currentSlug: string;
}) => {
	const isActive = item.node.slug === currentSlug;

	return (
		<div className="relative">
			<div
				className={`group flex items-center rounded-full px-4 py-3 mb-2
					transition-all duration-300 cursor-pointer
					${isActive
						? "bg-[#dfdfdf] text-black"
						: "text-[#545454] hover:bg-[#e9e9e9]"
					}`}
			>
				<Link
					href={`/products/${item.node.slug}`}
					className={`flex-1 transition-colors duration-200 text-[16px] hover:text-black
						${isActive ? "text-black" : "text-[#545454]"}
						`}
				>
					{locale === defaultLocale ? item.node.name : item.node.translation?.name || item.node.name}
				</Link>
			</div>
		</div>
	);
};

const Categories = ({ locale, categoriesData }: { locale?: string; categoriesData: CategoriesProps }) => {
	const t = useTranslations();
	const { slug: currentSlug } = useParams();

	// 获取当前路径对应的二级分类（子分类）
	const getCurrentSubCategories = useMemo(() => {
		if (!categoriesData?.edges) return [];

		// 如果没有 currentSlug，说明是在 /products/ 页面，显示所有二级分类
		if (!currentSlug) {
			const allSubCategories: CategoryEdge[] = [];

			categoriesData.edges.forEach((parentCategory) => {
				if (parentCategory.node.children?.edges) {
					// 对子分类进行排序
					const sortedChildren = [...parentCategory.node.children.edges].sort((a: any, b: any) => {
						if (!a.node.sortNumber && b.node.sortNumber) return 1;
						if (a.node.sortNumber && !b.node.sortNumber) return -1;
						if (!a.node.sortNumber && !b.node.sortNumber) return 0;
						return a.node.sortNumber - b.node.sortNumber;
					});
					allSubCategories.push(...sortedChildren);
				}
			});

			return allSubCategories;
		}

		// 查找当前 slug 对应的分类
		let currentCategory: CategoryEdge | null = null;
		let parentCategory: CategoryEdge | null = null;

		// 遍历所有一级分类
		for (const category of categoriesData.edges) {
			// 检查是否是一级分类本身
			if (category.node.slug === currentSlug) {
				currentCategory = category;
				break;
			}

			// 检查是否是该一级分类下的二级分类
			if (category.node.children?.edges) {
				const foundChild = category.node.children.edges.find(
					child => child.node.slug === currentSlug
				);
				if (foundChild) {
					parentCategory = category;
					currentCategory = foundChild;
					break;
				}
			}
		}

		// 如果当前是一级分类，返回其下的所有二级分类
		if (currentCategory && !parentCategory) {
			if (currentCategory.node.children?.edges) {
				return [...currentCategory.node.children.edges].sort((a: any, b: any) => {
					if (!a.node.sortNumber && b.node.sortNumber) return 1;
					if (a.node.sortNumber && !b.node.sortNumber) return -1;
					if (!a.node.sortNumber && !b.node.sortNumber) return 0;
					return a.node.sortNumber - b.node.sortNumber;
				});
			}
		}

		// 如果当前是二级分类，返回同级的所有二级分类
		if (currentCategory && parentCategory) {
			if (parentCategory.node.children?.edges) {
				return [...parentCategory.node.children.edges].sort((a: any, b: any) => {
					if (!a.node.sortNumber && b.node.sortNumber) return 1;
					if (a.node.sortNumber && !b.node.sortNumber) return -1;
					if (!a.node.sortNumber && !b.node.sortNumber) return 0;
					return a.node.sortNumber - b.node.sortNumber;
				});
			}
		}

		return [];
	}, [categoriesData, currentSlug]);

	return (
		<div className="w-full">
			{/* 二级分类列表 */}
			<div className="space-y-2">
				{getCurrentSubCategories.length > 0 ? (
					getCurrentSubCategories.map((item) => (
						<SubCategoryItem
							key={item.node.id}
							item={item}
							currentSlug={currentSlug as string}
							locale={locale}
						/>
					))
				) : (
					<div className="text-center text-gray-500 py-4">
						{t('common.noCategories') || '暂无分类'}
					</div>
				)}
			</div>
		</div>
	);
};

export default Categories;
