{"kind": "FETCH", "data": {"headers": {"access-control-allow-credentials": "true", "alt-svc": "quic=\":443\"; h3=\":443\"; h3-29=\":443\"; h3-27=\":443\";h3-25=\":443\"; h3-T050=\":443\"; h3-Q050=\":443\";h3-Q049=\":443\";h3-Q048=\":443\"; h3-Q046=\":443\"; h3-Q043=\":443\"", "connection": "keep-alive", "content-encoding": "gzip", "content-type": "application/json", "date": "Wed, 30 Jul 2025 07:00:05 GMT", "referrer-policy": "same-origin", "server": "nginx", "strict-transport-security": "max-age=31536000", "transfer-encoding": "chunked", "x-content-type-options": "nosniff"}, "body": "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", "status": 200, "url": "https://gz-gpjj-saleor.pinshop.com/graphql/"}, "revalidate": 60, "tags": []}