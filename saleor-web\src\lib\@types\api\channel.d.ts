import { BaseMicroservicesResponse } from "@/lib/@types/api/base";
import { TLocale } from "@/lib/@types/locale";
import { ChannelsListQuery } from "@/gql/graphql";

export declare namespace Channel {
	interface IChannelLanguageMapping {
		channel_language_mapping: Record<string, string>;
	}

	interface IChannelMap extends BaseMicroservicesResponse<IChannelLanguageMapping> {}

	// 这里是获取语言和获取频道列表后，将语言code做为key，频道信息做为value
	interface ILanguageMapChannel {
		[key: string]: ChannelsListQuery["channels"][number];
	}
}
