import { Link } from "@/navigation.ts";
import { defaultLocale } from "@/config.ts";
import React, { useEffect, useState } from "react";
import { useCategory } from "@/context/CategoriesContext.tsx";



const CategoriesMenuTwo=({locale}:{locale:string})=> {
	const { categories } = useCategory();



  
	return <div
		className=" invisible opacity-0 group-hover:opacity-100 group-hover:visible group-hover:scale-y-100 max-h-[500px]   absolute  left-0 bg-gray-50 w-screen px-3 py-5  rounded-b-sm   border-t border-t-gray-300 shadow transition-all duration-500 ease-in-out transform scale-y-0  origin-top ">
		<ul className="grid grid-cols-5 gap-2   container">
			{categories?.edges.map(item => {
				return  <li key={item.node.id}
									 className="  relative ">
					<Link href={"/products/" + item.node.slug} className="text-black flex  items-center justify-between ">
						<div className="flex items-center gap-x-2 border-b border-b-gray-400 py-2 hover:border-b hover:border-b-main">
							<div
								className="font-bold">{locale === defaultLocale ? item.node.name : (item.node.translation.name || item.node.name)}</div>
						</div>
					</Link>
					{
						item.node.children.edges.length > 0 && <div
							className="w-full   transition-all transform duration-700   rounded-b-sm   overflow-hidden  z-10 ">
							<ul className="  w-full grid grid-cols-1">
								{item.node.children?.edges.map(item => {
									return <li key={item.node.id}
														 className="w-full  inline-block    transition-all duration-700 p-1  relative">
										<Link href={'/products/' + item.node.slug} className="w-full text-black flex    items-center ">
											<div className="">{item.node.name}</div>
										</Link>
									</li>;
								})}

							</ul>
						</div>
					}

				</li>;
			})}

		</ul>

	</div>
}

export default CategoriesMenuTwo
