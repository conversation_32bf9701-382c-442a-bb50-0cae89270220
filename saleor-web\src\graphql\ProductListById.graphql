query ProductListByIds($first: Int = 9, $channel: String!, $locale: LanguageCodeEnum!, $ids: [ID!]) {
	products(first: $first, channel: $channel, where: { ids: $ids },sortBy: {direction: DESC, field: DATE}) {
		totalCount
		edges {
			node {
        		attributes {
			attribute {
				name
				translation(languageCode: $locale) {
					name
				}
			}
			values {
				name
				value
				translation(languageCode: $locale) {
					name
				}
				inputType
				file {
					contentType
					url
				}
			}
		}
		variants {
			...ProductDetailVariantItem
		}
				...ProductListItem
			}
		}
	}
}
