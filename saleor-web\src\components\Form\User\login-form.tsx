"use client";
import React from "react";
import { Button } from "@/components/Button";
import { SubmitHandler, useForm } from "react-hook-form";
import { RingLoader } from "react-spinners";
import { getCookie, setCookie } from "cookies-next";
import axios from "axios";
import { useTranslations } from "next-intl";
import { message } from 'antd';
import { UserLoginInfo, useUserStore } from "@/store/user.store";
import { useUserAuth } from "@/lib/hooks/useUserAuth";
import {useRouter  } from "@/navigation";

type FormValues = {
  email?: string;
  password?: string;
  remember?: boolean;
  onSubmit: (data?: React.BaseSyntheticEvent<object, any, any> | undefined) => Promise<void>;
  setLoginModalOn?: any;
};

interface LoginFormProps {
  setLoginModalOn?: any;
}

const LoginForm = ({ setLoginModalOn }: LoginFormProps) => {
  const t = useTranslations();
  const { setUserLoginInfo } = useUserStore();
  let get_form_info: any = getCookie("created__user__info");
  if (get_form_info) {
    get_form_info = JSON.parse(get_form_info);
  }
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset
  } = useForm<FormValues>();

  const [loading, setLoading] = React.useState(false);
  const { useLogin } = useUserAuth();
	const router = useRouter();
  const onSubmit: SubmitHandler<FormValues> = async (data:any) => {
    setLoading(true);

      try {
        setLoading(true);
        await useLogin(data);
        setLoginModalOn(false);
        // router.replace("/");
      } catch (e: any) {
        message.error(e?.message || "error");
      } finally {
        setLoading(false);
      }
  };

  return (
    <div className="w-full">
      <div className="mt-1 md:mt-4 user">
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-2 md:space-y-4">
          {/* 邮箱字段 */}
          <div>
            <label htmlFor="email" className="block text-left text-xs md:text-sm font-medium text-gray-700 mb-1 md:mb-1.5">
              {t("common.Email")} <span className="text-red-500">*</span>
            </label>
            <input
              type="email"
              id="email"
              className={`w-full px-2 py-1.5 md:px-3 md:py-2 text-xs md:text-sm placeholder:text-xs md:placeholder:text-sm placeholder:text-gray-300
                border rounded-sm outline-none bg-white text-black transition-colors duration-200
                focus:ring-1 md:focus:ring-2 focus:ring-themePrimary500 focus:border-transparent
                ${errors.email ? "border-red-500" : "border-themeSecondary300"}`}
              defaultValue={get_form_info?.email}
              placeholder={t("form.109a2790cc6b48461908f883b7b41549f9b3")}
              {...register("email", { required: t("common.Email_Required") })}
            />
            {errors.email && (
              <p className="mt-0.5 text-xs text-red-500">{errors.email.message}</p>
            )}
          </div>

          {/* 密码字段 */}
          <div>
            <label htmlFor="password" className="block text-left text-xs md:text-sm font-medium text-gray-700 mb-1 md:mb-1.5">
              {t("common.Password")} <span className="text-red-500">*</span>
            </label>
            <input
              type="password"
              id="password"
              className={`w-full px-2 py-1.5 md:px-3 md:py-2 text-xs md:text-sm placeholder:text-xs md:placeholder:text-sm placeholder:text-gray-300
                border rounded-sm outline-none bg-white text-black transition-colors duration-200
                focus:ring-1 md:focus:ring-2 focus:ring-themePrimary500 focus:border-transparent
                ${errors.password ? "border-red-500" : "border-themeSecondary300"}`}
              placeholder={t("common.Password")}
              {...register("password", { required: t("common.Password_Required") })}
            />
            {errors.password && (
              <p className="mt-0.5 text-xs text-red-500">{errors.password.message}</p>
            )}
          </div>

          {/* 提交按钮 */}
          <div className="pt-1 md:pt-3">
            <Button
              disabled={loading}
              className={`flex gap-2 md:gap-3 items-center justify-center w-full py-2 md:py-3 text-xs md:text-sm font-medium
                bg-black text-white rounded-sm transition-all duration-200 hover:bg-gray-800
                disabled:opacity-50 disabled:cursor-not-allowed
                ${loading ? "bg-themeSecondary800" : ""}`}
            >
              {loading && <RingLoader color="#fff" size={14} />}
              {loading ? t("message.7f9e518a7a1d1e4bc3e8990bed1d9be4d404") + "..." : t("common.Sign_in")}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default LoginForm;
