import { RegionType } from "@/lib/@types/api/region";

const baseUrl = process.env.NEXT_PUBLIC_AI_URL;

// 获取国家json
export const getCountryJson = async (locale: string) => {
	try {
		const res = (await fetch(`${baseUrl}/api/v1/ml_web/get_translate_country_json?language_code=${locale}`, {
			method: "POST",
			cache: "no-store",
		}).then((r) => r.json())) as RegionType.BaseResp<{ country: RegionType.Country[] }>;
		return res.result.country;
	} catch (e) {
		return null;
	}
};

// 获取国家下面的地区json
export const getRegionJson = async (isoCode: string, locale: string) => {
	try {
		const res = (await fetch(
			`${baseUrl}/api/v1/ml_web/get_translate_region_json?language_code=${locale}&iso_code=${isoCode}`,
			{
				method: "POST",
				cache: "no-store",
			},
		).then((r) => r.json())) as RegionType.BaseResp<{ region: RegionType.Region[] }>;
		return res.result.region;
	} catch (e) {
		return null;
	}
};
