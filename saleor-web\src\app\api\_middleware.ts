// pages/api/_middleware.js
import { NextResponse } from 'next/server';

export function middleware(req) {
	const res = NextResponse.next();

	// 设置CORS头
	res.headers.set('Access-Control-Allow-Origin', '*');  // 在生产环境中可能需要更严格的设置
	res.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
	res.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
	res.headers.set('Access-Control-Allow-Credentials', 'true');

	// 处理OPTIONS预检请求
	if (req.method === 'OPTIONS') {
		return new Response(null, { status: 200 });
	}

	return res;
}
