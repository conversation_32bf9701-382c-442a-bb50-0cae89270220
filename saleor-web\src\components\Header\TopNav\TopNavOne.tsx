"use client";

import React, { useEffect, useState } from "react";
// import Image from 'next/image';
import * as Icon from "@phosphor-icons/react/dist/ssr";
import { useLocale, useTranslations } from "next-intl";
import { redirect } from "next/navigation";
import FlagIcon from "../../Flag/index";
import { SITE_LOCALES } from "@/lib/constant";
import { type TLocale } from "@/lib/@types/locale";
import { defaultLocale } from "@/config";
import { Link } from "@/navigation";
import { LinkedinLogo, TiktokLogo } from "@phosphor-icons/react";
import { SocialList } from "@/components/Social/FollowUs";
import Image from "next/image";
import Search from "../Menu/search";
import { useRouter } from "next/navigation";
import NavUser from "@/components/User/nav-user";
import { useShoppingCartStore } from "@/lib/store/shoppingCart";
import CountBadge from "@/components/Badge/count-badge";
import CartDrawer from "@/components/Product/ShoppingCart/cartDrawer";
import { useShoppingCart } from "@/lib/hooks/useShoppingCart";
import { getInquiry } from "@/lib/utils/util";
import { useLoveStore } from "@/lib/store/love.store";
import { menus } from "@/lib/menu";
import clsx from "clsx";
import useMenuMobile from "@/store/useMenuMobile";
import { contactInfo, contactObj } from "@/lib/contacts";
import Translate from "@/components/Translate";
// Import Swiper React components
import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay, Navigation } from "swiper/modules";
// Import Swiper styles
import "swiper/css";
import Svg from "../Menu/Svg";
import { useCompareStore } from "@/lib/store/Compare.store";
import MobileContact from "@/components/MobileContact";
import MobileLogin from "@/components/MobileContact/MobileLogin";
import { executeGraphQL } from "@/lib/graphql";
import { handleGraphqlLocale } from "@/lib/utils/util";
import { ProductCategoriesDocument, ProductCategoriesQuery } from "@/gql/graphql";
// 引入Ant Design图标和组件
import { XOutlined, FacebookOutlined, InstagramFilled, YoutubeFilled, LinkedinFilled, TwitterOutlined, TikTokOutlined } from "@ant-design/icons";
import { Collapse } from "antd";

// 定义缓存数据类型接口
interface CategoryCache {
	data: {
		edges: Array<{
			node: {
				id: string;
				slug: string;
				name: string;
				translation?: {
					name: string;
				};
				children?: {
					edges: Array<{
						node: {
							id: string;
							slug: string;
							name: string;
							translation?: {
								name: string;
							};
						}
					}>;
				};
			}
		}>;
		length?: number;
	};
	timestamp: number;
}

interface Props {
	props: string;
	slogan?: string;
}

const TopNavOne: React.FC<Props> = ({ props, slogan }) => {
	const { findCheckout } = useShoppingCart();
	const t = useTranslations();
	const [currentSlug, setCurrentSlug] = useState("");
	const locale = useLocale();
	const { openMenuMobile, handleMenuMobile } = useMenuMobile();

	//喜欢
	const [loveCount, setLoveCount] = useState(0);
	const { loveIds } = useLoveStore();
	//对比
	const [CompareCount, setCompareCount] = useState(0);
	let { compareIds } = useCompareStore()
	// 购物车
	const { cartList } = useShoppingCartStore();
	const [count, setCount] = useState(0);
	const [Inquirycount, setInquirycount] = useState(0);
	//是否变色
	const [isVisible, setIsVisible] = useState<boolean>(false);

	useEffect(() => {
		// 1. 刷新购物车数据
		findCheckout("default-channel");

		const handleResize = () => {
			let Inquirydata = getInquiry();
			setInquirycount(Inquirydata.length);
		};
		handleResize();
		window.addEventListener("storage", handleResize);
		const handleScroll = () => {
			const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

			if (currentSlug == "/") {
				// 在首页，根据滚动位置设置 isVisible
				if (scrollTop > 230) {
					setIsVisible(true);
				} else {
					setIsVisible(false);
				}
			} else {
				setIsVisible(true);
			}
		};

		window.addEventListener("scroll", handleScroll);
		// Cleanup function to remove event listener
		return () => {
			window.removeEventListener("storage", handleResize);
			window.removeEventListener("scroll", handleScroll);
		};
	}, [currentSlug]);

	const currentLocales: TLocale[] = SITE_LOCALES;

	let router = useRouter();

	useEffect(() => {
		setCount(cartList?.lines?.length || 0);
	}, [cartList]);

	const getFlag = (code: string) => {
		return SITE_LOCALES.find((item) => {
			return item.code === code;
		})!.flag;
	}

	useEffect(() => {
		setLoveCount(() => {
			return loveIds.length > 99 ? 99 : loveIds.length;
		});
		setCompareCount(() => {
			return compareIds.length > 99 ? 99 : compareIds.length;
		});
	}, [loveIds, compareIds]);



	const GoToPage = (url: string) => {
		router.push(url);
		handleMenuMobile();
		// setExpandedCategory(null); // 重置展开状态
	};

	// 保留分类数据获取功能，但不在导航栏直接显示
	const [categories, setCategories] = useState<any>([]);

	// 从本地存储获取缓存的分类数据（保留后台数据获取功能，为产品页面做准备）
	useEffect(() => {
		let isMounted = true;

		// 尝试从本地存储获取缓存的分类
		try {
			const cachedData = localStorage.getItem('category_cache');
			if (cachedData) {
				const { data, timestamp } = JSON.parse(cachedData) as CategoryCache;
				if (data && data.edges && data.edges.length > 0) {
					setCategories(data);
				}
			}
		} catch (error) {
			console.error("Error loading cached categories:", error);
		}

		// 从服务器获取最新分类数据
		const fetchCategory = async () => {
			try {
				const result = await executeGraphQL(ProductCategoriesDocument, {
					withAuth: false,
					variables: { locale: handleGraphqlLocale(locale || defaultLocale), first: 10 },
				});
				if (result && result.categories && isMounted) {
					// 更新分类数据
					const newData = result.categories;
					console.log('Categories loaded:', newData); // 调试信息
					setCategories(newData);

					// 更新缓存
					try {
						localStorage.setItem('category_cache', JSON.stringify({
							data: newData,
							timestamp: Date.now()
						} as CategoryCache));
					} catch (error) {
						console.error("Error caching categories:", error);
					}
				}
			} catch (error) {
				console.error("Failed to fetch categories:", error);
			}
		};
		fetchCategory();

		// 清理函数
		return () => {
			isMounted = false;
		};
	}, [locale]);
	return (
		<>
			<div className={`top-nav`}>
				<div className={clsx("w-full bg-white/80 backdrop-blur-xl border-b border-white/20 shadow-sm")}>
					<div className="container box-border h-[60px] max-xl:hidden">
						<div className="flex h-full items-center text-white">
							{/* 左侧 Logo - 固定宽度 */}
							<div className="flex items-center justify-start flex-shrink-0 w-[240px]">
								<Link href={"/"} className="">
									<Image
										src={locale === "zh-Hans" || locale === "zh-Hant" ? "/image/logo-zh.png" : "/image/logo.png"}
										width={1000}
										height={1000}
										alt={`${process.env.NEXT_PUBLIC_COMPANY_NAME} logo`}
										className="object-contain w-[130px] h-[60px]"
										priority
										unoptimized
									></Image>
								</Link>
							</div>

							{/* 中间导航 - 自适应宽度，居中 */}
							<nav className="flex-1 flex justify-center h-full max-xl:hidden">
								<ul className="flex h-full items-center gap-[50px] max-xl:gap-[20px] whitespace-nowrap">
									{menus.map((item) => {
										// 如果是 Home 菜单项，在其后面插入分类菜单
										if (item.id === 1 && item.show) { // Home 菜单项
											return (
												<React.Fragment key={item.id}>
													{/* Home 菜单项 */}
													<li className="group h-full">
														<Link
															href={item.link}
															className="flex h-full items-center justify-center text-[14px] text-white duration-300 max-lg:text-[17px]"
														>
															<div
																title={t(item.name)}
																className={clsx(
																	"text-center text-[14px] hover:text-main text-[#282828] relative after:absolute after:bottom-0 after:left-0 after:h-[2px] after:w-full after:origin-right after:scale-x-0 after:transform after:bg-main after:transition-transform group-hover:after:origin-left group-hover:after:scale-x-100 duration-300"
																)}
															>
																{t(item.name)}
															</div>
														</Link>
													</li>

													{/* 横向展示的分类菜单 */}
													{categories?.edges?.length > 0 ? (
														categories.edges.map((category) => (
															<li key={category.node.id} className="group h-full">
																<Link
																	href={`/products/${category.node.slug}`}
																	className="flex h-full items-center justify-center text-[14px] text-white duration-300 max-lg:text-[17px]"
																>
																	<div
																		title={locale === defaultLocale
																			? category.node.name
																			: (category.node.translation?.name || category.node.name)
																		}
																		className={clsx(
																			"text-center text-[14px] hover:text-main text-[#282828] relative after:absolute after:bottom-0 after:left-0 after:h-[2px] after:w-full after:origin-right after:scale-x-0 after:transform after:bg-main after:transition-transform group-hover:after:origin-left group-hover:after:scale-x-100 duration-300"
																		)}
																	>
																		{locale === defaultLocale
																			? category.node.name
																			: (category.node.translation?.name || category.node.name)
																		}
																	</div>
																</Link>
															</li>
														))
													) : (
														// 加载中或无数据时显示占位符
														<li className="group h-full">
															<div className="flex h-full items-center justify-center text-[14px] text-gray-400">
																{t('common.loading') || 'Loading...'}
															</div>
														</li>
													)}
												</React.Fragment>
											);
										}

										// 其他菜单项正常显示（排除产品菜单和 Home 菜单，因为已经单独处理）
										return (
											item.show && item.id !== 20 && item.id !== 1 && (
												<li className={`group h-full `} key={item.id}>
													<Link
														href={item.link}
														className="flex h-full items-center justify-center text-[14px] text-white duration-300 max-lg:text-[17px]"
													>
														<div
															title={t(item.name)}
															className={clsx(
																"text-center text-[14px] hover:text-main text-[#282828] relative after:absolute after:bottom-0 after:left-0 after:h-[2px] after:w-full after:origin-right after:scale-x-0 after:transform after:bg-main after:transition-transform group-hover:after:origin-left group-hover:after:scale-x-100 duration-300"
															)}
														>
															{t(item.name)}
														</div>
													</Link>
												</li>
											)
										);
									})}
								</ul>
							</nav>

							{/* 右侧功能按钮 - 固定宽度，右对齐 */}
							<div className="flex items-center justify-end gap-2 flex-shrink-0 w-[240px]">
								{/* 搜索 */}
								<Search isVisible={isVisible} />

								{/* 用户 */}
								<div className="c-flex cursor-pointer  px-2 max-xl:hidden">
									<NavUser>
										<div className="c-flex h-[42px]  rounded-full ">
											<div className="s-flex gap-x-2 text-black">
												<i
													className={clsx(
														"ri-user-line  !text-xl text-[#282828] hover:text-black",
													)}
												></i>
											</div>
										</div>
									</NavUser>
								</div>

								{/* 对比 */}
								{/* <div className="max-lg:hidden">
									<Link
										href={"/compare"}
										className="c-flex relative group  h-[42px] w-[42px]  rounded-full  text-black hover:!text-mainColor"
									>
										<Svg isScrolled={true} ishead={true} />
										{!!CompareCount && (
											<span className="c-flex absolute right-0 top-0 h-[18px] w-[18px] rounded-full bg-[#d53a3d] text-[10px] text-white">
												{CompareCount}
											</span>
										)}
									</Link>
								</div> */}

								{/* /爱心/ */}
								<li className="c-flex cursor-pointer max-xl:!hidden">
									<Link
										href={"/collection"}
										className="c-flex relative  h-[42px] w-[42px]  rounded-full  text-[#282828] hover:!text-black"
									>
										<i
											className={clsx(
												"ri-heart-3-line ri-xl hover:!text-black text-[#282828]"
											)}
										></i>
										{!!loveCount && (
											<span className="c-flex absolute right-[6px] top-[4px] h-[8px] w-[8px] text-[14px] rounded-full bg-[#d53a3d] text-white">
												{/* + */}
											</span>
										)}
									</Link>
								</li>
								{/* <li className="c-flex cursor-pointer px-2 max-md:!hidden">
									<Link href={"/inquiry"} className="text-black ">
										<div className="c-flex   ">
											<i
												className={clsx(
													`ri-file-list-line ri-xl relative text-[#282828] hover:!text-main `
												)}
											>
												<CountBadge count={Inquirycount} />
											</i>
										</div>
									</Link>
								</li> */}

								{/* 购物车 */}
								{process.env.NEXT_PUBLIC_SITE_TYPE == "toc" && (
									<li className="c-flex cursor-pointer px-2 max-xl:!hidden">
										<CartDrawer>
											<div className="c-flex   ">
												<i
													className={clsx(
														`ri-shopping-bag-line ri-xl  relative text-[#282828] hover:text-black`,
													)}
												>
													<CountBadge count={count} />
												</i>
											</div>
										</CartDrawer>
									</li>
								)}
								<div className="max-xl:hidden flex-shrink-0">
									{process.env.NEXT_PUBLIC_IS_I18N == "true" && <Translate />}
								</div>
							</div>
						</div>
					</div>
				</div>

				{/* 手机端 */}
				<div className="relative mx-auto hidden h-[54px] bg-white/80 backdrop-blur-xl border-b border-white/20 px-[16px] max-xl:block max-xl:!w-full">
					<div className="header-main flex h-full w-full items-center">
						{/* 左侧菜单按钮 */}
						<div className="menu-mobile-icon flex items-center xl:hidden cursor-pointer" onClick={handleMenuMobile}>
							<i className="ri-align-left text-2xl text-black"></i>
							{/* <em className="text-blackmx-2 mx-2 text-black">{t("nav.MENU")}</em> */}
						</div>

						{/* 居中的logo */}
						<div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 transform">
							{/* 移动端logo */}
							<Link href={"/"} className="flex items-center !text-black">
								<Image
									src={locale === "zh-Hans" ? "/image/logo-zh.png" : "/image/logo.png"}
									width={200}
									height={42}
									alt={`${process.env.NEXT_PUBLIC_COMPANY_NAME} logo`}
									className="object-contain w-[120px] h-[54px]"
									priority
								></Image>
							</Link>
						</div>

						{/* 右侧搜索按钮 */}
						<div className="ml-auto flex items-center gap-2">
							<Search isVisible={isVisible} />
						</div>
					</div>
				</div>

				{/* 移动端菜单遮罩层 */}
				{openMenuMobile && (
					<div
						className="fixed inset-0 z-40 bg-black bg-opacity-50 xl:hidden"
						onClick={handleMenuMobile}
					/>
				)}
				{/* 移动端抽屉菜单 */}
				<div
					id="menu-mobile"
					className={`relative box-border bg-white/95 backdrop-blur-xl pt-6 overflow-y-auto overflow-x-hidden ${openMenuMobile ? "open" : ""}`}
				>
					<div className="menu-container w-full px-4">
						<div className="menu-main h-full overflow-hidden w-full">
							{/* 关闭按钮 */}
							<div className="heading relative mb-6">
								<div
									className="close-menu-mobile-btn bg-gray-100 cursor-pointer flex h-8 w-8 items-center justify-center rounded-full ml-auto"
									onClick={handleMenuMobile}
								>
									<i className="ri-close-line text-xl text-gray-600"></i>
								</div>
							</div>

							{/* 菜单列表 */}
							<div className="list-nav">
								<div className="flex-grow overflow-y-auto py-2">
									<ul className="divide-y divide-gray-100">
										{menus.map((item) => {
											// 如果是 Home 菜单项，在其后面插入分类菜单
											if (item.id === 1 && item.show) { // Home 菜单项
												return (
													<React.Fragment key={item.id}>
														{/* Home 菜单项 */}
														<li>
															<div
																onClick={() => GoToPage(item.link)}
																className="flex items-center px-4 py-3 text-gray-800 text-base font-medium rounded-md hover:bg-gray-50 transition-colors cursor-pointer"
															>
																<span>{t(item.name)}</span>
															</div>
														</li>

														{/* 移动端分类菜单 */}
														{categories?.edges?.length > 0 ? (
															categories.edges.map((category) => (
																<li key={category.node.id}>
																	<div
																		onClick={() => GoToPage(`/products/${category.node.slug}`)}
																		className="flex items-center px-4 py-3 text-gray-700 text-base font-normal rounded-md hover:bg-gray-50 hover:text-main transition-colors cursor-pointer border-l-2 border-transparent hover:border-main"
																	>
																		<span>
																			{locale === defaultLocale
																				? category.node.name
																				: (category.node.translation?.name || category.node.name)
																			}
																		</span>
																	</div>
																</li>
															))
														) : (
															// 加载中显示
															<li>
																<div className="flex items-center px-4 py-3 text-gray-400 text-base">
																	<span>{t('common.loading') || 'Loading categories...'}</span>
																</div>
															</li>
														)}
													</React.Fragment>
												);
											}

											// 其他菜单项正常显示（排除产品菜单和 Home 菜单）
											return (
												item.show && item.id !== 20 && item.id !== 1 && (
													<li key={item.id}>
														<div
															onClick={() => GoToPage(item.link)}
															className="flex items-center px-4 py-3 text-gray-800 text-base font-medium rounded-md hover:bg-gray-50 transition-colors cursor-pointer"
														>
															<span>{t(item.name)}</span>
														</div>
													</li>
												)
											);
										})}
									</ul>
								</div>


								<MobileContact GoToPage={GoToPage} handleMenuMobile={handleMenuMobile} />


							</div>
							<MobileLogin handleMenuMobile={handleMenuMobile} />
						</div>
					</div>
				</div>
			</div>
		</>
	);
};

export default TopNavOne;
