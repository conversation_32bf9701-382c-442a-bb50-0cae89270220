import { NextResponse } from "next/server";
import { object, string } from "yup";
import { defaultLocale } from "@/config";
import { translateStaticProps } from "@/lib/utils/translate";
import { executeGraphQL } from "@/lib/utils/graphql";
import { PasswordChangeDocument } from "@/gql/graphql";
import { EXP_MSG, PASSWORD_LENGTH } from "@/lib/constant";

/**
 * 处理密码更改请求的异步函数
 * @param req 请求对象，包含密码更改所需的信息
 * @returns 返回一个包含操作结果的响应对象
 */
export const POST = async (req: Request) => {
	// 获取请求头中的语言
	const locale = req.headers.get("accept-language")?.split(",")[0] || defaultLocale;
	// 获取请求头中的token
	const token = req.headers.get("authorization")?.split(" ")[1];
	try {
		// 如果没有token，抛出错误
		if (!token) {
			throw new Error(EXP_MSG);
		}
		// 解析请求体，提取新旧密码
		const body = (await req.json()) as {
			newPassword: string;
			oldPassword: string;
		};

		// 确保新密码与旧密码不同
		if (body.newPassword === body.oldPassword) {
			throw new Error("New password cannot be the same as the old password");
		}
		// 定义密码验证规则
		const schema = object({
			newPassword: string()
				.required("{new password} is required")
				.min(
					PASSWORD_LENGTH.min,
					`{new password} The character limit is between ${PASSWORD_LENGTH.min}-${PASSWORD_LENGTH.max} bits`,
				)
				.max(
					PASSWORD_LENGTH.max,
					`{new password} The character limit is between ${PASSWORD_LENGTH.min}-${PASSWORD_LENGTH.max} bits`,
				),
			oldPassword: string()
				.required("{old password} is required")
				.min(
					PASSWORD_LENGTH.min,
					`{old password} The character limit is between ${PASSWORD_LENGTH.min}-${PASSWORD_LENGTH.max} bits`,
				)
				.max(
					PASSWORD_LENGTH.max,
					`{old password} The character limit is between ${PASSWORD_LENGTH.min}-${PASSWORD_LENGTH.max} bits`,
				),
		});
		// 验证密码规则
		await schema.validate(body);
		// 执行密码更改的GraphQL操作
		const resp = await executeGraphQL(PasswordChangeDocument, {
			variables: {
				oldPassword: body.oldPassword,
				newPassword: body.newPassword,
			},
			headers: {
				Authorization: `Bearer ${token}`,
			},
		});
		// 检查密码更改是否成功
		if (resp.passwordChange?.errors?.length) {
			throw resp.passwordChange?.errors[0];
		}
		// 返回成功响应
		return NextResponse.json({
			code: 200,
			msg: "success",
			data: {
				user: resp.passwordChange?.user,
			},
		});
	} catch (e: any) {
		// 处理异常情况
		let message: string = e?.message || "Error";
		const code = e?.code?.startsWith("JWT_") || message === EXP_MSG ? 403 : 500;
		// 翻译错误信息
		message = (await translateStaticProps([{ message }], ["message"], defaultLocale, locale))[0]
			.message as string;
		// 返回错误响应
		return NextResponse.json({ code, msg: message, data: e?.errorResponse || null });
	}
};
