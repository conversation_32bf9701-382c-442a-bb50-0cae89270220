"use client";

import Skeleton from "react-loading-skeleton";
import Price from "@/components/Price/price";
import { BodyText } from "@/components/BodyText";
import Badge from "@/components/Badge";
import ProductAttributes from "./product-attributes";
// import ProductCartBuyInquiry from "@/components/Product/product-cart-buy-inquiry";

import { Link } from "@/navigation";
// import ProductVariable from "@/components/Product/product-variable";
// import { useProductVariableStore } from "@/lib/store/product-variable.store";
// import { useProductAttributeStore } from "@/lib/store/product-attribute.store";
import { useLocale, useTranslations } from "next-intl";
import { useEffect, useRef, useState } from "react";
import { observeElementIntersection, productTranslationName } from "@/lib/utils/util";
import { debounce } from "@/lib/utils";
import { SocialShare } from "../SingleBlog/SocialShare";
import Rating from "react-rating";
import { useSelect } from "@react-three/drei";
import ProductCartBuyInquiry from "./product-cart-buy-inquiry";
import ProductInfo from "./ProductInfo";
import { defaultLocale } from "@/config";
import { Modal, Popconfirm } from "antd";
import { contactInfo, payarr } from "@/lib/contacts";
import { useModalInquiryContext } from "@/context/ModalInquiryContext";

type Props = {
	product: any | null;
};

export default function Information({ product }: Props) {
	let locale = useLocale();
	const [isClient, setIsClient] = useState(false);
	const [VariantActive, setVariantActive] = useState<any>(null);
	const [Variant, setVariant] = useState<any>(null);
	const [modal2Open, setModal2Open] = useState(false);
const { setVariantActive3DName } = useModalInquiryContext();

	const t = useTranslations();
	const productSelectRef = useRef<HTMLDivElement | null>(null);
	// const { setSelectAttribute } = useProductAttributeStore();
	const handlerAttribute = (item: any) => {
		setVariantActive(item);
		setVariantActive3DName(item.name)

	};
	// const { currentProduct } = useProductVariableStore();

	useEffect(() => {
		setIsClient(true);
		if (product?.variants?.length > 0) {
			setVariant(product.variants[0]);
		}
	}, [product]);

	let isvariants = product?.variants?.some((item) => item.quantityAvailable > 0);
	const description: any = product?.description ? JSON.parse(product?.description) : null;
	const descriptionJson: any = product?.translation?.descriptionJson
		? JSON.parse(product?.translation?.descriptionJson)
		: null;

	if (!isClient) {
		return <div className="min-h-[400px]"><Skeleton count={5} /></div>;
	}

  // console.log(VariantActive,'VariantActive');
  let productName=locale == defaultLocale ? productTranslationName(product?.name):productTranslationName(product?.translation?.name) || productTranslationName(product?.name)
  
	return (
		<div className="relative max-w-[772px] flex-1 max-md:px-3">
			<div id="product-select">
				{product?.name ? (
					<h2 className="text-[26px]  font-medium text-black md:leading-10 ">
						{productName}
					</h2>
				) : (
					<>
						<Skeleton width={"100%"} height={40} />
						<Skeleton width={"100%"} height={40} />
					</>
				)}
				{
					<div className="flex flex-col flex-wrap items-start justify-center gap-2 py-3 md:gap-4">
						<div className="c-flex gap-2">
							{product && (
								<div className={`rounded-sm border-[1px] border-[#000] px-3 py-2 ${!!isvariants ? "" : ""}`}>
									{isvariants ? t("nav.IN STOCK") : t("nav.OUT OF STOCK")}
								</div>
							)}
							<div>{t("nav.Multiple")}</div>
						</div>
						<div className="c-flex">
							{product ? (
								<>
									<Price price={VariantActive?.pricing?.price.gross.amount || ""} className="text-[22px]" />
									{/* <div className="hidden h-8 w-0.5 bg-themeSecondary200 md:block"></div> */}
								</>
							) : (
								<Skeleton width={120} height={30} />
							)}
						</div>
					</div>
				}

				<div className="proSel-options">
					{product
						? description?.blocks[1]?.data?.text && (
								<>
									<div className="mt-5 w-full border border-themeSecondary200" />
									<BodyText
										innerHTML={
											locale === defaultLocale
												? description?.blocks[1]?.data?.text
												: descriptionJson?.blocks[1]?.data?.text || ""
										}
										className="mt-5 text-base font-normal text-themeSecondary500"
									></BodyText>
									<div className="mt-5 w-full border border-themeSecondary200" />
								</>
							)
						: [0, 1, 2].map((i) => {
								return <Skeleton width={"100%"} height={30} className="mt-2" key={i} />;
							})}

					{product?.variants?.length > 0 && (
						<ProductAttributes options={product?.variants} changeValue={handlerAttribute} />
					)}
				</div>

				{/* 添加 */}
				<div className="mb-3">
					<ProductCartBuyInquiry {...product} product={product} VariantActive={VariantActive} />
				</div>

				<Share modal2Open={modal2Open} setModal2Open={setModal2Open} />

				{/* 商品的属性 */}
				{/* <ProductInfo product={product} /> */}
			</div>
		</div>
	);
}

function Share({ modal2Open, setModal2Open }) {
	const t = useTranslations('nav');

	return (
		<>
			<div className="flex gap-6 Information mb-3">
				<div
					onClick={() => setModal2Open(true)}
					className="text-md  flex cursor-pointer items-center gap-x-2"
				>
					<Car /> <span>{t('DeliveryReturn')}</span>
				</div>
				<Popconfirm   
					icon={null}
					placement="topLeft"
					title={<SocialShare />}
					okText={null}
					cancelText={null}
					showCancel={false}
				>
					<div className="text-md  flex cursor-pointer items-center gap-x-2">
						<i className="ri-share-line text-xl "></i> <span>{t('Share')}</span>
					</div>
				</Popconfirm>
			</div>

			<div className="text-md  flex cursor-pointer items-center gap-x-2 max-md:flex-col max-md:justify-start max-md:items-start">
				<div className="flex ">
					<i className="ri-secure-payment-fill  text-2xl"></i>  
					<span>{t('Guarantee Safe Checkout')}</span>
				</div>
   
				<div className="flex gap-x-3">
					{payarr.map(item=>(
						<img key={item.img} src={item.img}  className="w-[50px] h-[30px]" alt={item.alt} />
					))}
				</div>
			</div>

			<Modal
				title=""
				centered
				open={modal2Open}
				onOk={() => setModal2Open(false)}
				onCancel={() => setModal2Open(false)}
				footer={null}
			>
				<h3 className="mb-8 text-xl">{t('Help')}</h3>
				<p className="mb-4">{t('Give us')}</p>
				<p className="mb-4">{t('Email')}: {contactInfo.email}</p>
				<p className="mb-4">{t('Phone')}: {contactInfo.phone}</p>
			</Modal>
		</>
	);
}

function Car() {
	return (
		<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
			<path d="M12 16V14" stroke="black" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
			<path d="M3 14V20C3 20.5523 3.44772 21 4 21H20C20.5523 21 21 20.5523 21 20V14" stroke="black" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
			<path d="M14.5 6.5H9.5C8.94772 6.5 8.5 6.94772 8.5 7.5V10.5C8.5 11.0523 8.94772 11.5 9.5 11.5H14.5C15.0523 11.5 15.5 11.0523 15.5 10.5V7.5C15.5 6.94772 15.0523 6.5 14.5 6.5Z" stroke="black" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
			<path d="M8.5 9H4C3.44772 9 3 9.44772 3 10V14H21V10C21 9.44772 20.5523 9 20 9H15.5" stroke="black" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
			<path d="M7 4V6.5" stroke="black" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
			<path d="M17 4V6.5" stroke="black" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
		</svg>
	);
}
