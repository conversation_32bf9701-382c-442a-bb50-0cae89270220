import { NextResponse } from "next/server";
import { object, string } from "yup";
import { defaultLocale } from "@/config";
import { translateStaticProps } from "@/lib/utils/translate";
import { executeGraphQL } from "@/lib/utils/graphql";
import { UserRegisterDocument, UpdateUserDetailsDocument } from "@/gql/graphql";
import { PASSWORD_LENGTH } from "@/lib/constant";
import { handleGraphqlLocale } from "@/lib/utils/util";

/**
 * 处理用户注册请求
 * @param req - 入站请求对象
 * @returns 返回注册结果的响应对象
 */
export const POST = async (req: Request) => {
	// 获取请求头中的首选语言，如果没有提供，则使用默认语言
	// const locale =  defaultLocale;
  		// 解析请求体为JSON格式，并指定期望的属性结构
      const body = (await req.json()) as {
        password: string;
        email: string;
        channel: string;
        locale: string;
        firstName?: string;
        lastName?: string;
        phone?: string;
        position?: string;
        companyName?: string;
      };
	try {

		// 初始化Yup对象，用于验证请求体中的各个字段
		// const schema = object({
		// 	password: string()
		// 		.required("{Password} is required")
		// 		.min(
		// 			PASSWORD_LENGTH.min,
		// 			`{new password} The character limit is between ${PASSWORD_LENGTH.min}-${PASSWORD_LENGTH.max} bits`,
		// 		)
		// 		.max(
		// 			PASSWORD_LENGTH.min,
		// 			`{new password} The character limit is between ${PASSWORD_LENGTH.min}-${PASSWORD_LENGTH.max} bits`,
		// 		),
		// 	email: string().email().required("{Email} is required"),
		// 	channel: string().required("{Channel} is required"),
		// });
		// // 验证请求体数据
		// await schema.validate(body);
		// 打印注册字段信息
		console.log("=== 注册字段信息 ===");
		console.log("接收到的完整请求体:", JSON.stringify(body, null, 2));
		console.log("发送给 GraphQL 的变量:", {
			email: body.email,
			password: body.password,
			channel: body.channel,
			locale: handleGraphqlLocale(body.locale),
		});
		console.log("额外的用户信息字段:", {
			firstName: body.firstName,
			lastName: body.lastName,
			phone: body.phone,
			position: body.position,
			companyName: body.companyName,
		});
		console.log("==================");

		// 准备 metadata 数组，包含额外的用户信息
		const metadata = [];
		if (body.phone) {
			metadata.push({ key: "phone", value: body.phone });
		}
		if (body.position) {
			metadata.push({ key: "position", value: body.position });
		}
		if (body.companyName) {
			metadata.push({ key: "companyName", value: body.companyName });
		}

		console.log("准备发送的 metadata:", metadata);

		// 执行GraphQL查询，参数基于请求体和语言设置
		const resp = await executeGraphQL(UserRegisterDocument, {
			variables: {
				email: body.email,
				password: body.password,
				channel: body.channel,
				locale: handleGraphqlLocale(body.locale),
				firstName: body.firstName || null,
				lastName: body.lastName || null,
				metadata: metadata.length > 0 ? metadata : null,
			},
			serverWithAuth: false,
		});
		console.log("resp", resp.accountRegister.errors);

		// 检查注册结果是否有错误
		if (resp.accountRegister?.errors?.length) {
			const errorMsg = resp.accountRegister?.errors[0]?.message || "Failed to register user";
			throw new Error(errorMsg);
		}

		// 获取注册成功的用户信息
		const registeredUser = resp.accountRegister?.user;

		// 从 metadata 中提取额外信息
		const userMetadata = registeredUser?.metadata || [];
		const extractedInfo = {
			phone: userMetadata.find(m => m.key === "phone")?.value || body.phone,
			position: userMetadata.find(m => m.key === "position")?.value || body.position,
			companyName: userMetadata.find(m => m.key === "companyName")?.value || body.companyName,
		};

		// 准备返回的响应数据
		const responseData = {
			code: 200,
			msg: "success",
			data: {
				user: registeredUser,
				additionalInfo: extractedInfo,
			},
		};

		// 打印返回给前端的响应数据
		console.log("=== 返回给前端的响应数据 ===");
		console.log("响应数据:", JSON.stringify(responseData, null, 2));
		console.log("========================");

		// 返回注册成功的响应
		return NextResponse.json(responseData);
	} catch (e: any) {
		// 处理异常，翻译错误信息
		let message: string = e?.message || "Error";
		message = (await translateStaticProps([{ message }], ["message"], defaultLocale, body.locale))[0].message;
		// 返回注册失败的响应
		return NextResponse.json({ code: 500, msg: message, data: null });
	}
};
