import React from "react";

function CountBadge({count = 0}) {
    return <>
        {
            count > 0 && <span className="absolute -top-[10px] right-0 p-0.5 bg-linksColor rounded-full text-white
                                    inline-flex items-center ring-1 ring-inset ring-gray-500/10 bg-[#d53a3d] justify-center w-[8px] h-[8px] !text-[14px] font-bold">
                                        {/* + */}
                                    </span>
        }
    </>;
}

export default React.memo(CountBadge);
