"use client";
import { Divide } from 'lucide-react';
import { useTranslations } from 'next-intl';
import React, { useState, useEffect } from 'react';

function Index() {
  const [isVisible, setIsVisible] = useState(false);
  const t = useTranslations('nav');
  useEffect(() => {
    // 检查本地存储中是否已经接受过 cookie
    const hasAccepted = localStorage.getItem('cookieAccepted');
    if (!hasAccepted) {
      setIsVisible(true);
    } else {
      // 如果已经同意过，初始化相关服务
      initializeServices();
    }
  }, []);

  // 初始化需要 cookie 同意的服务
  const initializeServices = () => {
    try {
      // 这里可以初始化各种需要用户同意的服务
      // 例如：Google Analytics、Facebook Pixel 等
      if (typeof window !== 'undefined') {
        // 设置允许所有 cookie
        document.cookie = "cookieConsent=accepted; max-age=31536000; path=/";
        
        // 可以触发一个自定义事件，让其他组件知道用户已同意
        const event = new CustomEvent('cookieConsentAccepted');
        window.dispatchEvent(event);

        // 记录用户同意的时间
        localStorage.setItem('cookieAcceptedAt', new Date().toISOString());
      }
    } catch (error) {
      console.error('Error initializing services:', error);
    }
  };

  const handleAccept = () => {
    try {
      // 保存用户同意状态
      localStorage.setItem('cookieAccepted', 'true');
      
      // 初始化服务
      initializeServices();
      
      // 最后隐藏提示框
      setIsVisible(false);
    } catch (error) {
      console.error('Error handling cookie acceptance:', error);
    }
  };

  if (!isVisible) return null;

  return (
      process.env.NEXT_PUBLIC_SITE_TYPE == "toc"&&<div className="max-md:hidden fixed bottom-5 right-5 z-[9999] max-w-[400px] bg-white/95 shadow-lg rounded-lg p-5 animate-slideIn">
      <div className="flex flex-col gap-4">
        <p className="text-sm leading-relaxed text-gray-700">
          {t('This website')}
          <span className="text-blue-600 hover:text-blue-800">
            {t('Read more')}
          </span>
        </p>
        <button 
          onClick={handleAccept} 
          className="self-end px-5 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors duration-200 font-medium"
        >
          {t('Accept')}
        </button>
      </div>
    </div>
    
    
  );
}

export default React.memo(Index);