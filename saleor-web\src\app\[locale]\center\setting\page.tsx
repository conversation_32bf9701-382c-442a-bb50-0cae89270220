"use client";
import { useEffect, useState } from "react";
import { useUpdateCustomer } from "@/lib/hooks/user/useUserUpdate";
import { Button } from "@/components/Button";
import { RingLoader } from "react-spinners";
import CenterLayout from "@/components/Layout/center-layout";
import { GetServerSideProps } from "next";
import { useTranslations } from "next-intl";
import { toast } from "react-toastify";
import axios from "axios";
import { getuserinfo, setuserinfo } from "@/lib/api/user";
import { useUserStore } from "@/store/user.store";
import { message } from "antd";
import { setCookie } from "cookies-next";
export default function Setting() {
	let { userInfo, setuserInfo } = useUserStore();

	const [userInfoUdate, setUserInfoUpdate] = useState({
		first_name: "", // 添加可
		last_name: "", // 添加可
		email: "",
		avatar_url: "",
	});
	const t = useTranslations();
	const [imagePreview, setImagePreview] = useState("");
	const [updateClickloading, SetUpdateClickSetLoading] = useState(false);
	const { customer, updateCustomer, updateCustomerBillingAddress } = useUpdateCustomer();

	const handleUserInfoUpdate = (e: any) => {
		const { name, value } = e.target;
		setUserInfoUpdate({ ...userInfoUdate, [name]: value });
		if (name === "avatar_url") {
			setUserInfoUpdate({ ...userInfoUdate, [name]: e?.target?.files[0] });
			// setImagePreview(URL.createObjectURL(e?.target?.files[0]));
		}
	};

	const handleUpdateClick = async (e: React.ChangeEvent<any>) => {
		e.preventDefault();
		SetUpdateClickSetLoading(true);

		// check if email field is not have any space
		if (userInfoUdate.email.includes(" ")) {
			message.error(t("message.db3a3da982203c489a8887afa25ca7dac6d5"));

			SetUpdateClickSetLoading(false);
			return;
		}

		setuserinfo(userInfoUdate.first_name, userInfoUdate.last_name, userInfo.token)
			.then((res) => {
				if (res) {
					console.log(res.accountUpdate.user, "res1");
					let obj = {
						...userInfo,
						...res.accountUpdate.user,
						username: `${res.accountUpdate.user.firstName} ${res.accountUpdate.user.lastName}`,
					};
					setuserInfo(obj);
					setCookie("__user__login__info", obj);

					message.success(t("message.39f9bcc42b4fd546c0e90d205a8134afc8c4"));
				}
			})
			.finally(() => {
				SetUpdateClickSetLoading(false);
			});
	};
	useEffect(() => {
		setUserInfoUpdate({
			// @ts-ignore
			first_name: customer?.result?.first_name || "",
			// @ts-ignore
			last_name: customer?.result?.last_name || "",
			// @ts-ignore
			email: customer?.result.email || "",
			// @ts-ignore
			avatar_url: customer?.result.avatar_url || "",
		});
	}, [customer]);
	useEffect(() => {
		let userOBj = {
			first_name: userInfo.firstName || null,
			last_name: userInfo.lastName || null,
			email: userInfo.email,
			avatar_url: "",
		};
		setUserInfoUpdate(userOBj);
	}, []);
	return (
		<CenterLayout>
			<div className="rounded-2xl border p-5 sm:p-10">
				<h3 className="font-bold">{t("center.be117347c0a11c43846a0eba6e842cbb5f0a")}</h3>
				<div className="lg:relative">
					<form action="">
						<div className="mt-6 flex flex-wrap items-start gap-5 lg:flex-nowrap lg:gap-10">
							<div className="grow space-y-5">
								<div className="flex flex-wrap items-center gap-5 sm:flex-nowrap flex-col w-[60%] max-md:w-full">
									<input
										type="text"
										placeholder={t("form.f788c9b20398a5481f38c08cf68f2cb8c964")}
										className="focus:shadow-outline h-12 w-full rounded-lg border px-5 focus:shadow-lg focus:outline-none"
										name="first_name"
										value={userInfoUdate.first_name}
										onChange={handleUserInfoUpdate}
									/>
									<input
										type="text"
										placeholder={t("form.2bb382a16eab9d446ac91af90ce99c3cc7f8")}
										className="focus:shadow-outline h-12 w-full rounded-lg border px-5 focus:shadow-lg focus:outline-none"
										name="last_name"
										value={userInfoUdate.last_name}
										onChange={handleUserInfoUpdate}
									/>
								</div>
								{/* <div className="flex flex-wrap sm:flex-nowrap items-center gap-5">
                <input
                  type="email"
                  placeholder={t("form.33f1e5508234f14946ebcb12416f5a73bd87")}
                  className="focus:outline-none border rounded-lg px-5 h-12 w-full focus:shadow-lg focus:shadow-outline"
                  name="email"
                  value={userInfoUdate.email}
                  onChange={handleUserInfoUpdate}
                />
              </div> */}
							</div>
						</div>
						<div onClick={handleUpdateClick} className="mt-8 w-fit">
							<Button className={`flex items-center gap-2 ${updateClickloading && "bg-themeSecondary800"}`}>
								{updateClickloading && <RingLoader color="#fff" size={20} />}
								{t("form.89d6993324b64541c93976dbb5de7af086c0")}
							</Button>
						</div>
					</form>
				</div>
			</div>
		</CenterLayout>
	);
}
