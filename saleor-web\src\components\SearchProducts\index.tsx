"use client";
import React, { useState, useEffect } from "react";
import ProductList from "@/components/ProductList/ProductList";
import MyEmpty from "@/components/MyEmpty";
import { useTranslations } from "next-intl";
import { useSearchParams } from "next/navigation";

import { fetchSearchProductsData, searchProducts } from "@/lib/api/product";
import SearchPagination from "./Pagination";

export default function Index({
	products: initialProducts,
	page: initialPage,
	searchValue: initsearchValue,
	channel,
	locale,
	total: initialTotal,
	limit: initialLimit,
}) {
	const t = useTranslations("nav");
	const searchParams = useSearchParams();
	const [products, setProducts] = useState(initialProducts);
	const [total, setTotal] = useState(initialTotal);
	const [loading, setLoading] = useState(false);
	const [pagination, setPagination] = useState({
		page: initialPage,
		limit: initialLimit,
	});
	const [searchValue, setSearchValue] = useState(initsearchValue);

	// 监听 query 变化
	useEffect(() => {
		const newSearchValue = searchParams.get("query") || "";
		setSearchValue(newSearchValue);
		setPagination({ page: 1, limit: pagination.limit }); // query 变了，重置到第一页
	}, [searchParams.get("query")]);

	// 监听 searchValue 或 page 变化，自动请求数据
	useEffect(() => {
		async function fetchData() {
			setLoading(true);
			try {
				const { detail } = (await searchProducts({
					searchValue,
					page: pagination.page,
					limit: pagination.limit,
				})) as any;
				const ids = detail?.res?.map((item) => item.product_id) || [];
				const { products } = await fetchSearchProductsData({ ids, locale, channel });
				setProducts(products);
				setTotal(detail.total);
			} catch (error) {
				console.error("Failed to fetch products:", error);
			} finally {
				setLoading(false);
			}
		}
		fetchData();
	}, [searchValue, pagination.page, pagination.limit]);

	// 分页
	function ChangePagination(page) {
		setPagination((prev) => ({ ...prev, page }));
	}

	return (
		<section className="mx-auto max-w-7xl p-8 pb-16">
			{products.totalCount && products.totalCount > 0 ? (
				<div>
					<h2 className="pb-8 text-xl font-semibold">Search results for &quot;{searchValue}&quot;:</h2>
					{loading ? (
						<div className="flex min-h-[65vh] items-center justify-center py-20">
							<div className="h-8 w-8 animate-spin rounded-full border-b-2 border-gray-900"></div>
							<span className="ml-2">Loading...</span>
						</div>
					) : (
						<ProductList key={pagination.page} channel={channel} locale={locale} products={products} />
					)}
					<SearchPagination page={pagination.page} total={total} ChangePagination={ChangePagination} />
				</div>
			) : (
				<div className="flex min-h-[50vh] flex-col items-center justify-center rounded-xl bg-white px-4 py-16 text-center shadow-sm ring-1 ring-gray-900/5">
					<div className="mx-auto max-w-lg">
						<MyEmpty
							link="/"
							text={t("No results found")}
							description={t("Try")}
							bottomText={t("Back to Home")}
							className="py-20 max-md:py-4"
						>
							<i className="ri-shopping-cart-2-fill text-4xl  !text-ddd"></i>
						</MyEmpty>
					</div>
				</div>
			)}
		</section>
	);
}
