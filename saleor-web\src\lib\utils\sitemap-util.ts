import { cApiUrl, defaultLocale, GetSeoMenusResponse, handleGraphqlLocale } from "@/lib/utils/util";
import { getBlogCls, getBlogSlug, getBlogTags } from "@/lib/api/blog";
import { ProductAllCategoriesListDocument, ProductCategoriesListDocument, ProductListPaginatedDocument } from "@/gql/graphql";
import { executeGraphQL } from "@/lib/utils/graphql";
import { getBlogList } from "../api/blog";
// import { getAllProductSlug, SlugInterface } from "@/lib/hooks/useGetProductSlug";
// import { getAllBlogSlug } from "@/lib/hooks/useGetBlogSlug";
// import { CustomPagesType, getAllCustomPage } from "@/lib/hooks/useGetAllCustomPage";
type RetType = {
	ret: GetSeoMenusResponse[];
};
export const getIsEditMenus = async (): Promise<string[]> => {
  try {
    const response = await fetch(cApiUrl + "/api/website/get_route_list");
    const { code, detail: { ret: data } } = await response.json() as any;

    if (code === 200) {
      console.log("res.data6666", data);
      // 直接返回处理好的数据
      return data.map((item: any) => item.link);
    }
    
    throw new Error("获取菜单失败");
  } catch (e) {
    console.error("getIsEditMenus Error:", e);
    return [];
  }
};

// 获取所有产品连接
export const getProductLinks = async () => {
  try {
    const slugs: string[] = [];
    let hasNextPage = true;
    let cursor = '';

    // 循环请求所有页面
    while (hasNextPage) {
      const productData = await executeGraphQL(ProductListPaginatedDocument, {
        withAuth: false,
        variables: {
          first: 100, // 每次请求的最大记录数
          after: cursor, // 使用当前游标，而不是空字符串
          // @ts-ignore
          locale: "EN",
          channel: "default-channel",
        },
        revalidate: 60,
      });
      // console.log(productData,'777');
      const edges = productData.products?.edges || [];
      const pageInfo = productData.products?.pageInfo;

      // 收集商品链接
      edges.forEach(edge => {
        if (edge?.node?.slug) {
          slugs.push(`${cApiUrl}/product/${edge.node.slug}`);
        }
      });

      // 更新分页信息
      hasNextPage = pageInfo?.hasNextPage || false;
      cursor = pageInfo?.endCursor || '';

      // 可选：添加延迟以避免请求过快
      if (hasNextPage) {
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    }


    
    return slugs;
    
  } catch (e) {
    console.error("getProductLinks Error:", e);
    return [];
  }
};

// 获取所有博客连接
export const getBlogLinks = async () => {
  try {
      const slugs: string[] = [];

  
    let data= await getBlogSlug({
      amount: 1000,
        page: 1
      });
      
      if(data.code==200){
        data.detail.ret.forEach(item => {
          slugs.push(`${cApiUrl}/blog/${item}`);
      });
      return slugs;
      }

      return [];
      
  } catch (e) {
    console.error("getBlogLinks Error:", e);
    return [];
  }
};

// 获取所有商品分类
export const getProductCategoriesLinks = async () => {
	let slugs = [];
	const allCategories = [];

	let hasNextPage = true;
	let cursor = "";

	while (hasNextPage) {
		const { categories } = await executeGraphQL(ProductAllCategoriesListDocument, {
			withAuth: false,
			variables: {
				after: cursor,
				locale: handleGraphqlLocale(defaultLocale),
				first: 100,
			},
			revalidate: 60,
		});

		const edges = categories.edges || [];
		const pageInfo = categories.pageInfo;

		edges.forEach((edge) => {
      slugs.push(`${cApiUrl}/products/${edge.node.slug}`);
		});

		hasNextPage = pageInfo?.hasNextPage || false;
		cursor = pageInfo?.endCursor || "";
	}

	return slugs || [];
};

// 获取所有blog分类
export const getBlogCategoriesLinks = async () => {
  const slugs: string[] = [];
  const clsList = await getBlogCls({ lang_code: { lang_code: 'en'}});
  clsList.forEach(item => {
    slugs.push(`${cApiUrl}/category/${item.cls_slug}`);
});
 return slugs||[]
};

  