

export declare namespace Blog {
	interface BaseResp<T> {
		code: number;
		message: string;
		detail: T;
	}

	interface Paging {
		page: number;
		limit: number;
	}
interface  GetBlogClsParams {
		lang_code: {
		lang_code: string
	},
		keyword?: string
}
interface GetBlogClsResp extends BaseResp<{
	cls_list:BlogCl[]
}>{}

	interface GetBlogTagResp extends BaseResp<{
		tag_list:BlogTag[]
	}>{}
	interface GetBlogListParams extends Paging {
		lang_code: {
			lang_code: string;
		};
		blog_title?: string; // 通过博客名称模糊查询博客列表
		blog_ids?: Array<number>; // 通过博客ID筛选博客列表
		tag_ids?: Array<number>; // 通过标签ID筛选博客列表
		cls_ids?: Array<number>; // 通过分类ID筛选博客列表
		tag_slug_in?: Array<{ slug: string }>; // 通过标签Slug筛选博客列表
		cls_slug_in?: Array<{ slug: string }>; // 通过分类Slug筛选博客列表
	}

	interface GetBlogListResp extends BaseResp<{
			blog_list: BlogListItem[];
			blog_filter_count: number;
		}> {}
	interface GetBlogCommentResp extends BaseResp<{
		ret:BlogComment[];
		comment_count:number;
	}>{}
interface  getBlogSlugResp extends BaseResp<{
		ret:string[]

}>{}
	interface BlogListItem {
		blog_id: number;
		blog_title: string;
		blog_excerpt: string;
		blog_thumb: string;
		blog_image_origin: string;
		blog_author: string;
		blog_slug: string;
		blog_upload_time: string;
		blog_tag_list: BlogTag[];
		blog_classification_list: BlogCl[];
    blog_cover_type:boolean;
    blog_cover_origin: string;
	}

	interface BlogCl {
		cls_id:number
		cls_name: string;
		cls_slug: string;
		banner_img_url:string;
		cls_parent:number;
		cls_related_count:number;
	}

	interface BlogTag {
		tag_id: number;
		tag_name: string;
		tag_slug: string;
	}

	interface GetBlogDetailResp
		extends BaseResp<{
			blog_content: BlogContent;
		}> {}

	interface GetBlogSeoResp
		extends BaseResp<{
			ret: BlogSeoItem[];
		}> {}

	interface BlogSeoItem {
		blog_id: number;
		blog_seoTitle: string;
		blog_seoDescription: string;
		blog_seoKeywords: string[];
		blog_slug: string;
		blog_cover_image: string;
		author: string;
	}

	interface BlogContent {
    update_time: any;
    cover_type: any;
    cover_url: string;
    cover_origin: string;

    blog_cover_origin: string;
    blog_cover_type: any;
		blog_id:number
		upload_time: string;
		thumb: string;
		image_url: string;
		blog_author: string;
		slug: string;
		blog_title: string;
		blog_excerpt: string;
		blog_content: string;
		blog_seoTitle: string;
		blog_seoDescription: string;
		blog_seoKeyword: string[];
		blog_classification_list: BlogClassIficationList[];
		blog_tag_list: BlogTagList[];
	}

	interface BlogTagList {
		tag_id: number;
		tag_slug: string;
		tag_name: string;
	}

	interface BlogClassIficationList {
		cls_id: number;
		cls_slug: string;
		cls_name: string;
	}
	interface  BlogComment {
		id: number,
		comment_content: string,
		comment_object_type: Boolean,
		comment_object_id: number,
		comment_post_time: string,
		comment_lang_code: string,
		comment_user_ip: string,
		comment_user_email: string,
		comment_user_first_name: string,
		comment_user_last_name: string,
		comment_user_company: string
	}
	interface BlogCommentItem {
		comment_content: string,
		comment_blog_id:string,
		comment_obj: Boolean,
		anonymous_comment_firstname: string,
		anonymous_comment_lastname: string,
		anonymous_comment_company: string,
		comment_lang_code: {
			lang_code: string
		}
	}
}


