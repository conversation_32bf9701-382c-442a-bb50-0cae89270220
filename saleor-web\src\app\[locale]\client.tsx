"use client";
import { useEffect, Suspense } from "react";
import { generateUuid } from "@/lib/utils/util";
import { useSessionStore } from "@/lib/store/session";
import {useRouter } from "@/navigation";
import { useUserStore } from "@/lib/store/user";
import { useUserStore as useUserStore2 } from "@/store/user.store";
/**
 * 客户端组件
 *
 * 该组件通过`useClient`指示React这是一个客户端组件它使用了React的`useEffect`钩子和自定义的UUID生成器
 * 主要用于在客户端初始化时设置或更新会话UUID
 */
function ClientContent() {
	// 从会话状态管理中解构出当前的会话UUID和设置会话UUID的函数
	const { session_uuid, setSessionUuid } = useSessionStore();
	const router = useRouter();
	const { setUserInfo } = useUserStore();
	const { userInfo } = useUserStore2();
	/**
	 * 在组件挂载或会话UUID变化时，从本地存储中加载会话状态并设置会话UUID
	 *
	 * 如果从本地存储加载的会话状态没有有效的UUID，则生成一个新的UUID并设置它
	 * 这确保了在客户端的会话始终有一个有效的UUID
	 */
	useEffect(() => {
		// 从本地存储中加载会话状态
		const session = JSON.parse(localStorage.getItem("session-store") || "") as any;
		// 如果会话状态不存在或没有有效的UUID，则生成并设置一个新的UUID
		!session?.state?.session_uuid && setSessionUuid(generateUuid());
	}, [session_uuid]);

	useEffect(() => {
		if (userInfo) {
			setUserInfo({
				user: {
					email: userInfo.user?.email,
					id: userInfo.user?.id,
				},
				token: userInfo.token,
			});
		}
	}, [router]);

	return null;
}

export default function Client() {
	return (
		<Suspense>
			<ClientContent />
		</Suspense>
	);
}
