// 获取基础页面的多语言seo
import {type MyPageProps, type SlugMyPageProps} from "@/lib/@types/base";
import {type SeoApiType} from "@/lib/@types/api/seo";
import {type BaseMicroservicesResponse} from "@/lib/@types/api/base";
import {type Blog} from "@/lib/@types/api/blog";
import {getChannelLanguageMap} from "@/lib/api/channel";
import {executeGraphQL} from "@/lib/utils/graphql";
import { GetProductCategorySeoDocument, GetProductSeoDocument } from "@/gql/graphql";
import {handleGraphqlLocale} from "@/lib/utils/util";
import {type ProductApiType} from "@/lib/@types/api/product";

const companyName = process.env.NEXT_PUBLIC_COMPANY_NAME;
const baseSeo = {
    title: companyName,
    description: companyName,
    keywords: [companyName]
};

export const getBasePageSeo = async (props: {
    params: MyPageProps["params"];
}): Promise<SeoApiType.SeoTDK> => {
    const urlPath = "/" + (props.params?.page?.join("/") || "");
    try {
        const baseUrl = process.env.NEXT_PUBLIC_LANGUAGE_LIST_URL;
        const res = (await fetch(`${baseUrl}/website/get_seo`, {
            method: "POST",
            headers: {
                "Content-Type": "application/json"
            },
            body: JSON.stringify({
                route: urlPath,
                lang_code: {
                    lang_code: props.params.locale
                }
            }),
            next:{ revalidate: 10*60 }
        }).then((r) => r.json())) as BaseMicroservicesResponse<SeoApiType.BasePageSeoResp>;
        if (res.code === 200) {
        return res.detail.ret[0].seo;
        } else {
            throw res;
        }
    } catch (e: any) {
        // @ts-ignore
        return baseSeo;
    }
};

export const getBlogPageSeo = async (props: SlugMyPageProps) => {
    try {
        const locale = props.params.locale;
        const slug = props.params.slug;
        const blogInfo = (await fetch(
            `${process.env.NEXT_PUBLIC_LANGUAGE_LIST_URL}/blog/get_blog_seo?lang_code=${locale}`,
            {
                method: "POST",
                headers: {
                    "Content-Type": "application/json"
                },
                body: JSON.stringify([slug])
            }
        ).then((r) => r.json())) as Blog.GetBlogSeoResp;
        if (blogInfo.code === 200 && blogInfo?.detail?.ret?.length) {
            const info = blogInfo.detail.ret[0];
            return {
                title: info.blog_seoTitle||'',
                description: info.blog_seoDescription,
                keywords: info.blog_seoKeywords,
                ogImg: info.blog_cover_image,
                author: info.author
            } ;
        }
        return baseSeo;
    } catch (e) {
        console.log(e);
        return baseSeo;
    }
};

export const getProductDetailSeo = async (props: SlugMyPageProps) => {
    try {
        const locale = props.params.locale;
        const slug = props.params.slug;
        const channel = (await getChannelLanguageMap())[locale];
        const {seo} = await executeGraphQL(GetProductSeoDocument, {
            variables: {
                locale: handleGraphqlLocale(locale),
                channel,
                slug,
                filterMetadataKey: `keywords`,
            },
            revalidate:5*60

        });
        if (!seo) return baseSeo;
        const media = seo?.media ? JSON.parse(seo?.media) as ProductApiType.MediaMetadata[] : null;
        let ogImg = "";
        if (media?.length) {
            ogImg = media[0].url;
        }
        const title = seo?.translation?.seoTitle || seo?.seoTitle || "";
        const description = seo?.translation?.seoDescription || seo?.seoDescription || "";

        const seoKeywordsJson:any = seo?.seoKeywords ? JSON.parse(seo?.seoKeywords) : null;
        const keywordStr = seoKeywordsJson?.lang?.[locale] || seoKeywordsJson?.lang?.['source'];
        const keywords = keywordStr ? keywordStr.split(',').map(k => k.trim()) : [];
        return {
            title,
            description,
            keywords: keywords || [],
            ogImg
        };
    } catch (e) {
        console.log(e);
        return baseSeo;
    }
};

export  const getProductCategorySeo=async (props: SlugMyPageProps)=>{
    try {
        const locale = props.params.locale;
        const slug = props.params.slug;
        const {seo} = await executeGraphQL(GetProductCategorySeoDocument, {
            variables: {
                locale: handleGraphqlLocale(locale),
                slug,
                filterMetadataKey: `keywords`,

            }
        });
        if (!seo) return baseSeo;
        const media = seo?.media ? JSON.parse(seo?.media) as ProductApiType.MediaMetadata[] : null;
        let ogImg = "";
        if (media?.length) {
            ogImg = media[0].url;
        }
        const title = seo?.translation?.seoTitle || seo?.seoTitle || "";
        const description = seo?.translation?.seoDescription || seo?.seoDescription || "";

        const seoKeywordsJson:any = seo?.seoKeywords ? JSON.parse(seo?.seoKeywords) : null;
        const keywordStr = seoKeywordsJson?.lang?.[locale] || seoKeywordsJson?.lang?.['source'];
        const keywords = keywordStr ? keywordStr.split(',').map(k => k.trim()) : [];
        return {
            title,
            description,
            keywords: keywords || [],
            ogImg
        };
    } catch (e) {
        console.log(e);
        return baseSeo;
    }
}
