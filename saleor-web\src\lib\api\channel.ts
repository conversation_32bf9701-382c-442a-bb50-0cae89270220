// 获取语言与频道

import { type Channel } from "@/lib/@types/api/channel";
import { executeGraphQL } from "@/lib/utils/graphql";
import { ChannelsListDocument } from "@/gql/graphql";

// 获取语言与频道映射列表
export const getChannelLanguageMap = () => {
	return new Promise((resolve, reject) => {
		const baseUrl = `${process.env.NEXT_PUBLIC_LANGUAGE_LIST_URL}/translation/channel_mapping`;
		fetch(baseUrl, {
			method: "GET",
			// @ts-ignore
			headers: {
				"Content-Type": "application/json",
				username: process.env.NEXT_PUBLIC_SOFT_USERNAME,
				ml_token: process.env.NEXT_PUBLIC_SOFT_TRANSLATE_MLTOKEN,
			},
			cache: "no-cache",
		})
			.then(async (r) => {
				const res = (await r.json()) as Channel.IChannelMap;
				if (res.code === 200) {
					resolve(res.detail.channel_language_mapping);
				} else {
					reject(res);
				}
			})
			.catch((err) => {
				console.error(err);
				reject(null);
			});
	}) as Promise<Channel.IChannelMap["detail"]["channel_language_mapping"]>;
};

// 获取频道列表
export const getChannelList = () =>
	executeGraphQL(ChannelsListDocument, {
		serverWithAuth: true,
	});
