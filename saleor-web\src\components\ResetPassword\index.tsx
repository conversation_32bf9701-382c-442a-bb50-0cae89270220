"use client";
import React, { useState, useEffect } from "react";
import { Input, But<PERSON>, App } from "antd";
import { LockOutlined, SafetyOutlined } from "@ant-design/icons";
import { motion } from "framer-motion";
import { useSearchParams } from "next/navigation";
import { useTranslations } from "next-intl";

const itemVariants = {
	hidden: { opacity: 0, y: 20 },
	visible: { opacity: 1, y: 0 },
};
const buttonVariants = {
	rest: { scale: 1 },
	hover: { scale: 1.05 },
	tap: { scale: 0.98 },
};
const containerVariants = {
	hidden: { opacity: 0 },
	visible: {
		opacity: 1,
		transition: {
			duration: 0.8,
			when: "beforeChildren",
			staggerChildren: 0.2,
		},
	},
};

export default function Index({ locale }) {
	const { message } = App.useApp();
	const searchParams = useSearchParams();
	const [password, setPassword] = useState("");
	const [confirmPassword, setConfirmPassword] = useState("");
	const [loading, setLoading] = useState(false);
	const uuid = searchParams.get("uuid");
	const t = useTranslations("common");

	const handleSubmit = async () => {
		if (!uuid) {
			message.error(t("Missing345"));
			return;
		}
		if (password !== confirmPassword) {
			message.error(t("Passwords346"));
			return;
		}
		setLoading(true);
		try {
			const response = await fetch(process.env.NEXT_PUBLIC_LANGUAGE_LIST_URL + "/saleor/saleor_pwd", {
				method: "POST",
				headers: { "Content-Type": "application/json" },
				body: JSON.stringify({
					mission_uuid: uuid,
					new_pwd: password,
				}),
			});
			// console.log(response, "response");

			if (response.ok) {
				message.success(t("Reset347"));
			} else {
				message.error(t("Failed348"));
			}
		} catch (error) {
			message.error(t("Network349"));
		} finally {
			setLoading(false);
		}
	};

	return (
		<motion.div
			initial="hidden"
			animate="visible"
			variants={containerVariants}
			className="Tourist0rderInquiryPage relative  w-full bg-gradient-to-b from-gray-50 to-white"
		>
			<div className="container mx-auto flex flex-col items-center px-4 pt-20">
				<motion.div
					variants={itemVariants}
					className="w-full max-w-2xl overflow-hidden rounded-2xl
                    bg-white
                    shadow-[0_20px_60px_-10px_rgba(0,0,0,0.05)]"
				>
					<div className=" px-8 py-12">
						<motion.h2
							variants={itemVariants}
							initial="hidden"
							animate="visible"
							className="mb-8 text-center text-3xl font-bold text-gray-800"
						>
							{t("Reset350")}
						</motion.h2>

						<motion.div variants={itemVariants} className="group relative mb-6">
							<Input.Password
								size="large"
								placeholder={t("Enter351")}
								value={password}
								onChange={(e) => setPassword(e.target.value)}
								prefix={<LockOutlined className="text-gray-400" />}
								className="mb-4 h-[56px] rounded-2xl border-transparent bg-gray-50 px-6 text-lg text-gray-700 transition-all duration-300 placeholder:text-gray-300 hover:bg-gray-100/80 focus:!border-none"
								autoComplete="off"
							/>
							<Input.Password
								size="large"
								placeholder={t("Confirm352")}
								value={confirmPassword}
								onChange={(e) => setConfirmPassword(e.target.value)}
								prefix={<LockOutlined className="text-gray-400" />}
								className="mb-4 h-[56px] rounded-2xl border-transparent bg-gray-50 px-6 text-lg text-gray-700 transition-all duration-300 placeholder:text-gray-3300 hover:bg-gray-100/80 focus:!border-none"
								autoComplete="off"
							/>
						</motion.div>

						<motion.div variants={buttonVariants} initial="rest" whileHover="hover" whileTap="tap">
							<Button
								type="primary"
								size="large"
								block
								onClick={handleSubmit}
								loading={loading}
								className="h-[56px] rounded-2xl border-none bg-[#4e2b75] bg-gradient-to-r text-lg font-medium shadow-[0_2px_12px_rgba(78,43,117,0.15)] transition-all duration-300 hover:opacity-95"
							>
								{loading ? t("Submit353") : t("Reset354")}
							</Button>
						</motion.div>
					</div>
				</motion.div>
			</div>
		</motion.div>
	);
}
