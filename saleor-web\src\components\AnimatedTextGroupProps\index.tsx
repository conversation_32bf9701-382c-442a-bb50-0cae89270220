"use client";
import { useEffect, useRef, useState } from 'react';
import gsap from 'gsap';
import { usePathname } from 'next/navigation';

interface AdvancedLoadingAnimationProps {
  text?: string;
  onComplete?: () => void;
  duration?: number;
  barColor?: string;
  textColor?: string;
  children: React.ReactNode;
}

export const AdvancedLoadingAnimation: React.FC<AdvancedLoadingAnimationProps> = ({
  text = "Zhong Cheng",
  onComplete,
  duration = 3.5,
  barColor = "white",
  textColor = "white",
  children
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const tlRef = useRef<GSAPTimeline>();
  const [isAnimationComplete, setIsAnimationComplete] = useState(false);
  const pathname = usePathname(); // 获取当前路径

  // 检查是否是 PayPal 相关路径
  const isPayPalPath = pathname?.includes('/paypal-state');


  useEffect(() => {
    const tl = gsap.timeline({
      onComplete: () => {
        setIsAnimationComplete(true);
        onComplete?.();
      }
    });
    tlRef.current = tl;

    // 初始化设置
    gsap.set('.loading-bar', {
      scaleY: 0,
      opacity: 0.7
    });

    // 初始化文字容器和文字
    gsap.set('.text-container', {
      opacity: 0  // 设置文字容器初始透明度为 0
    });

    gsap.set('.text-char', {
      opacity: 1,
      scale: 0,
      filter: 'blur(10px)'
    });

    // 动画序列
    tl
      .to('.loading-bar', {
        scaleY: 1,
        duration: 1,
        stagger: {
          each: 0.1,
          ease: 'power4.out'
        }
      })
      .to('.loading-bar', {
        width: '100%',
        opacity: 0.3,
        duration: 0.8,
        ease: 'power2.inOut'
      })
      // 添加文字容器的显示动画
      .to('.text-container', {
        opacity: 1,
        duration: 0.5
      })
      .to('.text-char', {
        opacity: 1,
        scale: 1,
        filter: 'blur(0px)',
        duration: 0.8,
        stagger: {
          each: 0.05,
          ease: 'back.out(1.7)'
        }
      })
      .to('.text-container', {
        scale: 100,
        duration: duration,
        ease: 'power2.inOut'
      });

    return () => {
      tlRef.current?.kill();
    };
  }, [onComplete, duration]);

  // 如果动画完成，显示children
  if (isAnimationComplete||isPayPalPath) {
    return <>{children}</>;
  }

  // 动画未完成，显示加载动画
  return (
    <div 
      ref={containerRef} 
      className="fixed inset-0 bg-black flex items-center justify-center overflow-hidden z-50"
    >
      <div className="absolute flex gap-1">
        {[...Array(7)].map((_, i) => (
          <div
            key={i}
            className="loading-bar origin-bottom"
            style={{
              width: '2.5rem',
              height: '60vh',
              backgroundColor: barColor,
              opacity: 0
            }}
          />
        ))}
      </div>

      <div 
        className="text-container relative font-bold tracking-wider"
        style={{ 
          color: textColor, 
          fontSize: '6rem',
          opacity: 0  // 设置初始样式为透明
        }}
      >
        {text.split('').map((char, i) => (
          <span 
            key={i} 
            className="text-char relative inline-block mx-1"
          >
            {char}
          </span>
        ))}
      </div>
    </div>
  );
}