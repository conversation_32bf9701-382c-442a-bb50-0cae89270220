// 询盘内容与邮箱屏蔽
export const get_inquiry_blocking_keyword = async () => {
	const url = `${process.env.NEXT_PUBLIC_LANGUAGE_LIST_URL}/website/inquiry_blocking_keyword`;
	const res = await fetch(url, {
		method: "GET",
		headers: {
			"Content-Type": "application/json",
		},
		next: { revalidate: 60000 },
	}).then((r) => r.json());
	// @ts-ignore
	if (res.code === 200) {
		// @ts-ignore
		return res.detail.res;
	} else {
		// @ts-ignore
		throw new Error(res.message);
	}
};
