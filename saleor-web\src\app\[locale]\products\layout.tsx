import { type ReactNode } from "react";
import { executeGraphQL } from "@/lib/graphql";
import { ProductCategoriesListDocument } from "@/gql/graphql";
import { handleGraphqlLocale } from "@/lib/utils/util";
import { defaultLocale } from "@/config";
import { SearchBar } from "@/components/SearchBar/page";
import { getChannelLanguageMap } from "@/lib/api/channel";
import { getTranslations, unstable_setRequestLocale } from "next-intl/server";

import ProductsLayoutPage from "@/components/ProductsLayoutPage";

type Props = {
	children: ReactNode;
	params: {
		locale: string;
	};
};

export default async function ProductLayout({ children, params }: Props) {
	const { locale } = params;
	const channel = (await getChannelLanguageMap())[defaultLocale];
	const { categories } = await executeGraphQL(ProductCategoriesListDocument, {
		withAuth: false,
		variables: {
			channel,
			locale: handleGraphqlLocale(locale || defaultLocale),
			first: 30,
		},
		revalidate: 60,
	});
  unstable_setRequestLocale(params.locale);
  

  
	return (
		<>
    <ProductsLayoutPage channel={channel} locale={locale} categoriesData={categories}>
    {children}
    </ProductsLayoutPage>
 
		</>
	);
}
