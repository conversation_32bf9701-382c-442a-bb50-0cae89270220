"use client";
import React, { useState, useEffect, useRef } from 'react';

//自定义鼠标
const CustomCursor = () => {
  const [position, setPosition] = useState({ x: 0, y: 0 });

  useEffect(() => {
    const updatePosition = (e:any) => {
      setPosition({ x: e.clientX, y: e.clientY });
    };

    document.addEventListener('mousemove', updatePosition);

    return () => {
      document.removeEventListener('mousemove', updatePosition);
    };
  }, []);

  return (
    <div
      style={{
        position: 'fixed',
        top: position.y + 'px',
        left: position.x + 'px',
        width: '50px',
        height: '50px',
        backgroundColor: 'rgba(0,0,0,.4)', // 自定义样式
        borderRadius: '50%',
        pointerEvents: 'none',
        zIndex: 9999,
        transform: 'translate(-50%, -50%)', // 调整位置到鼠标中心
      }}
      className='flex items-center justify-center'
    >
      <i className="ri-play-mini-fill text-white text-xl"></i>
    </div>
  );
};

const CustomCursorWrapper = ({ children }:any) => {
  const [isOver, setIsOver] = useState(false);
  const [isClicked, setIsClicked] = useState(false);
  const timeoutRef = useRef<NodeJS.Timeout>();
  const handleMouseEnter = () => {
    setIsOver(true);
  };

  const handleMouseLeave = () => {
    setIsOver(false);
  };



  return (
    <div
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      style={{ cursor: 'none' }}
      className='w-full h-full '
    >
     {children}
      {isOver && <CustomCursor />}
    </div>
  );
};

export default CustomCursorWrapper;
