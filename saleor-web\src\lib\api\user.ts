import {  AccountAddressCreateDocument, AccountAddressDeleteDocument, AccountAddressUpdateDocument, AddressSetDefaultDocument, AddressValidationRulesDocument, CountryCode, GetUserAddressesDocument, MyQueryDocument, UpdateUserDetailsDocument } from "@/gql/graphql";
import { executeGraphQL } from "../graphql";

// 获取用户
export const getuserinfo = async (id:string,token:string) => {
	const resp = await executeGraphQL(MyQueryDocument, {
		withAuth: false,
		variables: {
			id:id,
		},
		headers: {
			Authorization: `Bearer ${token}`,
		},
	});

	return resp;
};

// 用户
export const setuserinfo = async (firstName:string,lastName:string,token:string) => {
	const resp = await executeGraphQL(UpdateUserDetailsDocument, {
		withAuth: false,
		variables: {
			firstName:firstName,
			lastName: lastName,
		},
    headers: {
			Authorization: `Bearer ${token}`,
		},
	});

	return resp;
};


// 修改BillingInfo
interface BillingInfo {
  city: string;
  companyName: string;
  country: CountryCode;
  countryArea: string;
  firstName: string;
  lastName: string;
  phone: string;
  streetAddress1: string;
  postalCode: string;
}


//创建地址
export const userAccountAddressCreate = async (object: any, token: string) => {
  const resp = await executeGraphQL( AccountAddressCreateDocument, {
    withAuth: false,
    variables: {
      ...object
    },
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });

  return resp;
};

// 获取所有地址
export const getAddresslist = async (token: string) => {

  const resp = await executeGraphQL(GetUserAddressesDocument, {
    withAuth: false,
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });

  return resp;
};



//设置默认地址
export const AddressDefault = async (object: any, token: string) => {
  const resp = await executeGraphQL( AddressSetDefaultDocument, {
    withAuth: false,
    variables: {
      ...object
    },
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });

  return resp;
};

//地址更新
export const Addressiupdate = async (object: any, token: string) => {
  const resp = await executeGraphQL( AccountAddressUpdateDocument, {
    withAuth: false,
    variables: {
      ...object
    },
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });

  return resp;
};

//del地址
export const AddressiDel= async (id: string, token: string) => {
  const resp = await executeGraphQL( AccountAddressDeleteDocument, {
    withAuth: false,
    variables: {
      id
    },
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });

  return resp;
};



//获取省
export const GETAddresscountryArea= async (countryCode: string, token: string) => {
  const resp = await executeGraphQL(AddressValidationRulesDocument, {
    withAuth: false,
    variables: {
      countryCode
    },
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });

  return resp;
};