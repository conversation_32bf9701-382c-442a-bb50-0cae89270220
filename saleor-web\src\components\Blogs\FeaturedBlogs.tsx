'use client'
import React, { useEffect, useState } from "react";
import { type Blog } from '@/lib/@types/api/blog';
import { getBlogList } from "@/lib/api/blog.ts";
import moment from "moment";
import { Link } from "@/navigation.ts";
import { RightOutlined } from "@ant-design/icons";
import { useTranslations } from "next-intl";

// 骨架屏组件
const SkeletonBlog = () => (
	<div className="flex items-start gap-4 p-4 rounded-lg border border-gray-100">
		{/* 图片骨架 */}
		<div className="flex-shrink-0 w-16 h-16 rounded-lg bg-gray-200" />
		<div className="flex-1 min-w-0 space-y-2">
			{/* 标题骨架 */}
			<div className="h-4 bg-gray-200 rounded-full w-3/4" />
			<div className="h-4 bg-gray-200 rounded-full w-1/2" />
			{/* 日期骨架 */}
			<div className="h-5 bg-gray-200 rounded-full w-24" />
		</div>
	</div>
);

const FeatureBlogs: React.FC<{locale:string}> = ({locale}) => {
	const [blogList, setBlogList] = useState<Blog.BlogListItem[]>([]);
	const [loading, setLoading] = useState(true);

	useEffect(() => {
		setLoading(true);
		getBlogList({
			lang_code: { lang_code: locale },
			page: 1,
			limit: 3
		} as Blog.GetBlogListParams)
		.then(res => {
			setBlogList(res?.detail?.blog_list || []);
		})
		.finally(() => {
			setLoading(false);
		});
	}, [locale]);

	const t = useTranslations();
	
	return (
		<div>
			<h3 className="text-lg font-bold text-gray-900 mb-4 flex items-center gap-2">
				<span className="w-2 h-2 bg-black rounded-full"></span>
				{t('nav.Featured Blogs')}
			</h3>

			{/* 加载状态 */}
			{loading && (
				<div className="space-y-4">
					{[...Array(3)].map((_, index) => (
						<SkeletonBlog key={index} />
					))}
				</div>
			)}

			{/* 加载完成且有数据 */}
			{!loading && blogList.length > 0 && (
				<div className="space-y-4">
					{blogList.map((item) => (
						<Link 
							key={item.blog_id} 
							href={'/blog/'+item.blog_slug} 
							className="group flex items-start gap-4 p-4 rounded-lg hover:bg-white hover:shadow-sm transition-all duration-300 border border-transparent hover:border-gray-200"
						>
							<div className="flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden bg-gray-100">
								<img 
									src={item.blog_cover_origin} 
									alt={item.blog_title} 
									className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300" 
								/>
							</div>
							<div className="flex-1 min-w-0">
								<h4 className="text-sm font-semibold text-gray-900 group-hover:text-black transition-colors duration-300 line-clamp-2 mb-2 leading-5">
									{item.blog_title}
								</h4>
								<time className="text-xs text-gray-500 font-medium bg-gray-100 px-2 py-1 rounded-full">
									{moment(item.blog_upload_time).format("MMM DD, YYYY")}
								</time>
							</div>
							<div className="flex-shrink-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
								<RightOutlined className="!text-gray-400 text-xs" />
							</div>
						</Link>
					))}
				</div>
			)}

			{/* 无数据状态 */}
			{!loading && blogList.length === 0 && (
				<div className="text-center py-8">
					<div className="text-gray-400 text-sm">No featured blogs available</div>
				</div>
			)}
		</div>
	);
};

export default FeatureBlogs;
