'use client'
import { <PERSON> } from "@/navigation";
import { Linked<PERSON><PERSON><PERSON>, <PERSON>ik<PERSON><PERSON><PERSON> } from "@phosphor-icons/react";
import React from "react";
import { contactObj } from "@/lib/contacts";
import { useTranslations } from "next-intl";

const FollowUs=({className,textClass}:{className?:string,textClass?:string})=> {
	const t=useTranslations()
	return <div className={`mt-6 flex  ${className}`}>
		<p className="font-semibold text-sm ">{t('base.followOn')}</p>
<SocialList className={textClass}></SocialList>
	</div>
}
  export const SocialList=({className}: { className?: string } )=> {
	return <div className="right-content flex items-center gap-5  ml-10">
		<Link href={contactObj.Tiktok} target="_blank">
			<TiktokLogo size={14} weight="fill" className={`text-white ${className}`} />
		</Link>
		<Link href={contactObj.Linkedin} target="_blank">
			<LinkedinLogo size={14} weight="fill" className={`text-white ${className}`} />
		</Link>
		<Link href={contactObj.Youtube} target="_blank">
			<i className={`ri-youtube-fill text-white text-lg ${className}`}></i>
		</Link>
		<Link href={contactObj.Twitter} target="_blank">
			<i className={`ri-twitter-fill text-white text-lg ${className}`}></i>
		</Link>
		<Link href={contactObj.Pinterest} target="_blank">
			<i className={`ri-pinterest-fill text-white text-lg ${className}`}></i>
		</Link>
		<Link href={contactObj.FaceBook} target="_blank">
			<i className={`ri-facebook-fill text-white text-lg ${className}`}></i>
		</Link>
	</div>
}

export default FollowUs
