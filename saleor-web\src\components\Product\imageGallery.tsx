"use client";

import React, { useEffect, useMemo, useState } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import Image from "next/image";
import { CaretLeft, CaretRight } from "@phosphor-icons/react";
import { Button, Empty, Skeleton } from "antd";
import { ProductZoom } from "./product-zoom";
import LoadNormalModal from "../3DNormalModal";
import { useTranslations } from "next-intl";

const imageWh = 612;
const spaceBetween = 8;
const slidesPerView = 5;

export const getImagesForProduct = (product) => {
	if (!product?.metadata) return [];
	return JSON.parse(product.media);
};

function ImageGallery({ product }) {
	const imagesList = getImagesForProduct(product) as any[];
	const [active, setActive] = useState(0);
	const [mainImage, setMainImage] = useState<any>(""); // current image
	const [images, setImages] = useState<any[]>([]);
	const [swiperExample, setSwiperExample] = useState<any>();
	const t = useTranslations();
	const changeProductImages = (product) => {
		if (product === null) {
			setImages([]);
			setMainImage("");
			setActive(0);
			return;
		}
		if (imagesList[0] && imagesList[0].url) {
			setMainImage(imagesList[0].url);
			setImages(imagesList);
		}
	};

	useEffect(() => {
		if (product) {
			changeProductImages(product);
			setActive(0);
		}
	}, [product]);

	const handlePrevOrNext = (type: "prev" | "next") => {
		if (type === "prev") {
			setActive((value) => {
				if (value === 0) return value;
				setMainImage(images[value - 1].url);
				swiperExample?.slidePrev();
				return value - 1;
			});
		} else {
			setActive((value) => {
				if (value + 1 > images.length - 1) return value;
				setMainImage(images[value + 1].url);
				if (value + 1 > slidesPerView - 1) {
					swiperExample?.slideNext();
				}
				return value + 1;
			});
		}
	};

	const setActiveImgIndex = (index: number) => {
		setActive((val) => {
			if (images.length > slidesPerView) {
				const prev = document.querySelector(".button-prev") as HTMLElement;
				const next = document.querySelector(".button-next") as HTMLElement;
				index > val ? next?.click() : prev?.click();
			}
			return index;
		});
	};

	const threeModel = useMemo<any>(() => {
		const json = product.metadata.find((item) => item.key === "threeModel");
		return json ? JSON.parse(json.value)[0] : null;
	}, [product]);

	const [threeDisplay, setThreeDisplay] = useState(false);
	const handleDisplay = () => {
		setThreeDisplay(!threeDisplay);
	};
  // console.log(product,'product');
	return (
		<div className="flex w-full select-none flex-col items-center">
			<div className="relative flex w-full items-center justify-center rounded-xl bg-[#f5f5f5]">
				{mainImage && images.length > 1 && (
					<div
						onClick={() => {
							handlePrevOrNext("prev");
						}}
						className="mr-16 flex size-12 cursor-pointer items-center justify-center rounded-full bg-white shadow hover:opacity-60 max-md:absolute max-md:left-1 max-md:hidden"
					>
						<CaretLeft size={28} weight="bold" className="" />
					</div>
				)}

				<div className="absolute right-2 top-5 z-20">
					<Button type="primary" shape="round" onClick={handleDisplay}>
						{t("product.toggle-3d-image")}
					</Button>
				</div>

				<div
					className={`absolute inset-0 rounded-xl bg-[#f5f5f5] transition duration-300 ease-in-out ${
						threeDisplay ? "invisible opacity-0" : "visible opacity-100"
					}`}
				>
					{threeModel && (
						<LoadNormalModal
								backgroundColor={threeModel.backgroundColor}
							modalUrl={process.env.NEXT_PUBLIC_LANGUAGE_LIST_URL + `/medias/file_download/` + threeModel.id}
							className="w-full"
							fileName={threeModel.name}
							metalness={threeModel.metalness}
							color={threeModel.color}
							roughness={threeModel.roughness}
						></LoadNormalModal>
					)}
				</div>

				<div
					style={{
						maxWidth: `${imageWh}px`,
						maxHeight: `${imageWh}px`,
					}}
					className={`z-10 h-fit w-fit transition duration-300 ease-in-out ${
						!threeDisplay ? "invisible opacity-0" : "visible opacity-100"
					}`}
				>
					{mainImage ? (
						<div className="group h-full">
							<ProductZoom
								src={mainImage}
								imageWidth={imageWh}
								imageHeight={imageWh}
								setActiveImgIndex={setActiveImgIndex}
								quality={100}
								imgList={images}
								fit="contain"
							/>
						</div>
					) : (
						<Skeleton paragraph={{ rows: 2 }} />
					)}
				</div>

				{mainImage && images.length > 1 && (
					<div
						onClick={() => {
							handlePrevOrNext("next");
						}}
						className="ml-16 flex size-12 cursor-pointer items-center justify-center rounded-full bg-white shadow hover:opacity-60 max-md:absolute max-md:left-1 max-md:hidden"
					>
						<CaretRight size={28} weight="bold" />
					</div>
				)}
			</div>

			<div
				style={{
					maxWidth: `${imageWh}px`,
					height: `${imageWh / 5}px`,
				}}
				className="relative mt-2 flex w-full items-center justify-between"
			>
				{images.length ? (
					<>
						{images.length > slidesPerView && (
							<>
								<div
									onClick={() => {
										handlePrevOrNext("prev");
									}}
									className={`button-prev ri-arrow-left-s-line ri-3x absolute left-[-50px] z-[2] cursor-pointer text-main max-lg:left-0 lg:z-[1]`}
								/>
								<div
									onClick={() => {
										handlePrevOrNext("next");
									}}
									className={`button-next ri-arrow-right-s-line ri-3x absolute right-[-50px] z-[2] cursor-pointer text-main max-lg:right-0 lg:z-[1]`}
								/>
							</>
						)}

						<Swiper
							onSwiper={setSwiperExample}
							className="w-full"
							spaceBetween={spaceBetween}
							slidesPerView={slidesPerView}
							navigation={{
								nextEl: ".button-next",
								prevEl: ".button-prev",
							}}
						>
							{images?.map((image: any, index: number) => (
								<SwiperSlide key={index}>
									<div
										className={`flex h-fit w-fit cursor-pointer items-center justify-center overflow-hidden rounded-md border-[3px] duration-300 ${
											active === index ? " border-black" : "border-themeSecondary200"
										}`}
										onMouseEnter={() => {
											setMainImage(image.url);
											setActive(index);
										}}
									>
										<div className="relative hidden aspect-square object-cover sm:flex sm:items-center sm:justify-center">
											<Image quality={100} src={image.url} width={100} height={100} alt={"product image "} />
										</div>
										<div className="relative flex aspect-square sm:hidden">
											<Image quality={100} src={image.url} width={100} height={100} alt={"product image "} />
										</div>
									</div>
								</SwiperSlide>
							))}
						</Swiper>
					</>
				) : (
					<Empty className="w-full" />
				)}
			</div>
		</div>
	);
}

 export default React.memo(ImageGallery);
