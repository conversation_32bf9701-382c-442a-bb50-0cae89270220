import { create } from "zustand";
import { persist } from "zustand/middleware";
import { sessionStoragePersistStorage } from "@/lib/utils/store";

type State = {
	openTranslateModal: boolean;
	setOpenTranslateModal: (open: boolean) => void;
	openMobileMenu: boolean;
	setOpenMobileMenu: (open: boolean) => void;
	openCartModal: boolean;
	setOpenCartModal: (open: boolean) => void;
	openInquiryModal: boolean;
	setOpenInquiryModal: (open: boolean) => void;
};

export const useSiteStore = create(
	persist<State>(
		(set, get) => ({
			openTranslateModal: false,
			setOpenTranslateModal: (open) => {
				set({ openTranslateModal: open });
			},
			openMobileMenu: false,
			setOpenMobileMenu: (open) => {
				set({ openMobileMenu: open });
			},
			openCartModal: false,
			setOpenCartModal: (open) => {
				set({ openCartModal: open });
			},
			openInquiryModal: false,
			setOpenInquiryModal: (open) => {
				set({ openInquiryModal: open });
			},
		}),
		{
			name: "site-store",
			storage: sessionStoragePersistStorage,
		},
	),
);
