fragment CategoryLocaleItem on Category {
	translation(languageCode: $locale) {
		name
		description
		seoDescription
		seoTitle
	}
}

fragment ProductLocaleItem on Product {
	translation(languageCode: $locale) {
		name
	}
}

fragment CollectionLocaleItem on Collection {
	translation(languageCode: $locale) {
		name
		description
		seoDescription
		seoTitle
	}
}

fragment ProductVariantLocaleItem on ProductVariant {
	translation(languageCode: $locale) {
		name
	}
}
