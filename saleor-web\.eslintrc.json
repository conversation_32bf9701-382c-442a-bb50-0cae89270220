{
	"$schema": "https://json.schemastore.org/eslintrc.json",
	"parser": "@typescript-eslint/parser",
	"plugins": ["@typescript-eslint"],
	"parserOptions": {
		"project": "./tsconfig.json"
	},
	"extends": [
		"plugin:@typescript-eslint/recommended"
	],
	"rules": {
		// 基础的 TypeScript 校验规则
//		"@typescript-eslint/no-unused-vars": ["warn", { "argsIgnorePattern": "^_", "varsIgnorePattern": "^_" }],
		"@typescript-eslint/no-empty-function": "warn", // 允许空函数，只警告
		"@typescript-eslint/ban-types": "off", // 禁用 ban-types 规则
		"@typescript-eslint/no-explicit-any": "off", // 允许使用 any 类型
		"@typescript-eslint/explicit-module-boundary-types": "off", // 关闭显式类型声明的要求

		// 允许 async 函数不使用 await
		"@typescript-eslint/require-await": "off",
		// 允许函数不在 try-catch 中使用 return await
		"@typescript-eslint/return-await": "off",
		"@typescript-eslint/ban-ts-comment": "off",
		"prefer-const": "off",
		"react-hooks/exhaustive-deps": "off",
		"@typescript-eslint/no-unnecessary-type-constraint": "off",
		"@typescript-eslint/no-unused-vars": "off",
"@typescript-eslint/no-this-alias": "off",
		"import/no-default-export": "off"

	},
//	"overrides": [
//		{
//			// 关闭特定文件中的 import 规则
//			"files": ["src/app/**/{page,layout,error,loading,not-found}.tsx", "*.ts","src/**/*.tsx"],
//			"rules": {
//				"import/no-default-export": "off"
//			}
//		}
//	],
	"ignorePatterns": ["*.js", "*.jsx", "*.cjs", "node_modules", "dist"]
}
