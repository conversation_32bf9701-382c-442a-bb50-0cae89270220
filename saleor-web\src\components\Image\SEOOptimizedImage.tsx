import React, { useState, useEffect } from 'react';
import Image from 'next/image';

interface SEOOptimizedImageProps {
    src: string;
    alt: string;
    width?: number;
    height?: number;
    className?: string;
    imageClassName?: string;
    style?: React.CSSProperties;
    loading?: 'eager' | 'lazy';
    priority?: boolean;
    sizes?: string;
    quality?: number;
    fill?: boolean;
    unoptimized?: boolean;
    itemProp?: string;
    imageStyle?: any;
}

/**
 * SEO优化的图片组件
 * 结合Next.js Image优化和SEO友好性
 * 使用noscript标签确保爬虫能看到图片信息
 */
const SEOOptimizedImage: React.FC<SEOOptimizedImageProps> = ({
    src,
    alt,
    width,
    height,
    className = '',
    imageClassName,
    style,
    loading = 'lazy',
    priority = false,
    sizes,
    quality = 75,
    fill = false,
    unoptimized = false,
    itemProp,
    imageStyle
}) => {
    const [isLoading, setIsLoading] = useState(true);

    // 当src变化时重置loading状态，并添加超时保护
    useEffect(() => {
        setIsLoading(true);
        
        // 添加超时保护，确保即使事件未触发也能关闭loading状态
        const timeoutId = setTimeout(() => {
            setIsLoading(false);
        }, 10000); // 10秒后自动关闭loading状态
        
        return () => clearTimeout(timeoutId);
    }, [src]);

    const handleLoad = () => {
        setIsLoading(false);
    };

    return (
        <>
            <div className={`relative ${className}`} style={style}>
                <Image
                    src={src}
                    alt={alt}
                    width={width}
                    height={height}
                    className={className}
                    {...(priority ? { priority: true } : { loading })}
                    sizes={sizes}
                    quality={quality}
                    fill={fill}
                    unoptimized={unoptimized}
                    itemProp={itemProp}
                    onLoad={handleLoad}
                    onError={handleLoad}
                    style={imageStyle}
                />
                {isLoading && !priority && loading === 'lazy' && (
                    <div className="absolute inset-0 flex items-center justify-center bg-white/30 backdrop-blur-sm">
                        <div className="h-8 w-8 animate-spin rounded-full border-4 border-solid border-gray-400 border-t-gray-800"></div>
                    </div>
                )}
            </div>

            {/* SEO友好的noscript版本 - 爬虫看到的 */}
            <noscript>
                <img
                    src={src}
                    alt={alt}
                    width={width}
                    height={height}
                    className={className}
                    style={style}
                    itemProp="image"
                    itemScope
                    itemType="https://schema.org/ImageObject"
                />
            </noscript>

            {/* 结构化数据 - 帮助搜索引擎理解图片 */}
            <script
                type="application/ld+json"
                dangerouslySetInnerHTML={{
                    __html: JSON.stringify({
                        "@context": "https://schema.org",
                        "@type": "ImageObject",
                        "contentUrl": src,
                        "name": alt,
                        "description": alt,
                        "width": width,
                        "height": height
                    })
                }}
            />
        </>
    );
};

export default SEOOptimizedImage; 