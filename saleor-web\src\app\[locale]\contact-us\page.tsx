import React from "react";
import { MyPageProps } from "@/lib/@types/base";
import type { Metadata } from "next";
import { getBasePageSeo } from "@/lib/api/seo";
import { generateSeo } from "@/lib/utils/seo";
import ConcatPage from "@/components/Contact/ConcatPage";
import { unstable_setRequestLocale } from "next-intl/server";
export const generateMetadata = async (props: MyPageProps): Promise<Metadata> => {
	props.params.page = ["contact-us"];
	// 获取基础页面的SEO数据
	const seo = await getBasePageSeo(props);
	// 生成最终的SEO信息，并返回
	return generateSeo(props, {
		...seo,
		ogType: "website",
	});
};

export default function Page({ params }: MyPageProps) {
	// const t = useTranslations();
	unstable_setRequestLocale(params.locale);
	return (
		<>
			<ConcatPage locale={params.locale} />
		</>
	);
}
