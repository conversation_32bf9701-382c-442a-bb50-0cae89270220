/* 公司介绍 */
.company-intro {
  background: #f8f9fa;
}

.intro-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}

.intro-text {
  .text-block {
    margin-bottom: 2.5rem;
    
    h3 {
      font-size: 1.5rem;
      font-weight: 600;
      color: #333;
      margin-bottom: 1rem;
      position: relative;
      
      &::after {
        content: '';
        position: absolute;
        bottom: -0.5rem;
        left: 0;
        width: 50px;
        height: 3px;
        background: linear-gradient(45deg, #667eea, #764ba2);
        border-radius: 2px;
      }
    }
    
    p {
      font-size: 1.1rem;
      line-height: 1.8;
      color: #555;
    }
    
    strong {
      color: #667eea;
      font-weight: 600;
    }
  }
}

.intro-image {
  position: relative;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0,0,0,0.1);
  
  img {
    width: 100%;
    height: 400px;
    object-fit: cover;
  }
  
  .image-caption {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0,0,0,0.8));
    color: white;
    padding: 2rem;
    
    h4 {
      font-size: 1.25rem;
      font-weight: 600;
      margin-bottom: 0.5rem;
    }
    
    p {
      font-size: 0.9rem;
      opacity: 0.9;
    }
  }
}

/* 发展历程 */
.timeline-section {
  background: #fff;
}

.timeline {
  position: relative;
  max-width: 800px;
  margin: 0 auto;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 4px;
    height: 100%;
    background: linear-gradient(to bottom, #667eea, #764ba2);
    border-radius: 2px;
  }
}

.timeline-item {
  position: relative;
  margin-bottom: 3rem;
  
  &.left {
    .timeline-content {
      margin-right: 60%;
      text-align: right;
    }
  }
  
  &.right {
    .timeline-content {
      margin-left: 60%;
      text-align: left;
    }
  }
}

.timeline-content {
  background: white;
  padding: 2rem;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0,0,0,0.1);
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    top: 50%;
    width: 0;
    height: 0;
    border: 15px solid transparent;
  }
  
  .timeline-item.left &::before {
    right: -30px;
    border-left-color: white;
    transform: translateY(-50%);
  }
  
  .timeline-item.right &::before {
    left: -30px;
    border-right-color: white;
    transform: translateY(-50%);
  }
}

.timeline-year {
  font-size: 2rem;
  font-weight: 700;
  color: #667eea;
  margin-bottom: 0.5rem;
}

.timeline-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
}

.timeline-description {
  font-size: 1rem;
  color: #666;
  line-height: 1.6;
}

.timeline-dot {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  background: #667eea;
  border: 4px solid white;
  border-radius: 50%;
  box-shadow: 0 0 0 4px #667eea;
}

/* 核心产品 */
.products-section {
  background: #f8f9fa;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.product-card {
  background: white;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0,0,0,0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  
  &:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
  }
}

.product-image {
  height: 200px;
  overflow: hidden;
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }
  
  .product-card:hover & img {
    transform: scale(1.1);
  }
}

.product-info {
  padding: 1.5rem;
  
  h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
  }
  
  p {
    font-size: 1rem;
    color: #666;
    line-height: 1.6;
  }
}

/* 荣誉资质 */
.honors-section {
  background: #fff;
}

.honors-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.honor-card {
  background: white;
  padding: 2rem;
  border-radius: 15px;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0,0,0,0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  cursor: pointer;
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
  }
}

.honor-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.honor-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  line-height: 1.4;
}

/* 技术创新 */
.innovation-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.innovation-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}

.innovation-stats {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.stat-card {
  background: rgba(255,255,255,0.1);
  padding: 1.5rem;
  border-radius: 15px;
  text-align: center;
  backdrop-filter: blur(10px);
  
  .stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: #ffd700;
    display: block;
  }
  
  .stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
    margin-top: 0.5rem;
  }
}

.innovation-highlights {
  h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
  }
  
  ul {
    list-style: none;
    padding: 0;
    
    li {
      position: relative;
      padding-left: 1.5rem;
      margin-bottom: 0.75rem;
      font-size: 1.1rem;
      line-height: 1.6;
      
      &::before {
        content: '✓';
        position: absolute;
        left: 0;
        color: #ffd700;
        font-weight: bold;
      }
    }
  }
}

.innovation-image {
  position: relative;
  border-radius: 20px;
  overflow: hidden;
  
  img {
    width: 100%;
    height: 400px;
    object-fit: cover;
  }
  
  .image-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0,0,0,0.8));
    color: white;
    padding: 2rem;
    
    h4 {
      font-size: 1.25rem;
      font-weight: 600;
      margin-bottom: 0.5rem;
    }
    
    p {
      font-size: 0.9rem;
      opacity: 0.9;
    }
  }
}

/* 合作伙伴 */
.partners-section {
  background: #f8f9fa;
}

.partners-carousel {
  margin-bottom: 3rem;

  .swiper {
    padding: 2rem 0;
  }

  .swiper-slide {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.partner-logo {
  width: 120px;
  height: 80px;
  background: white;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
  transition: transform 0.3s ease;

  &:hover {
    transform: translateY(-5px);
  }

  img {
    max-width: 80%;
    max-height: 60%;
    object-fit: contain;
    filter: grayscale(100%);
    transition: filter 0.3s ease;
  }

  &:hover img {
    filter: grayscale(0%);
  }
}

.partners-text {
  text-align: center;

  p {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #666;
    max-width: 800px;
    margin: 0 auto;
  }
}

/* 图片画廊 */
.gallery-section {
  background: #fff;
}

.gallery-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.gallery-item {
  position: relative;
  height: 250px;
  border-radius: 15px;
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.3s ease;

  &:hover {
    transform: scale(1.05);

    .gallery-overlay {
      opacity: 1;
    }
  }

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.gallery-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.gallery-icon {
  font-size: 2rem;
  color: white;
}

/* 联系我们 */
.contact-section {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  color: white;
}

.contact-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}

.contact-info {
  .contact-item {
    display: flex;
    align-items: center;
    margin-bottom: 2rem;

    .contact-icon {
      font-size: 2rem;
      margin-right: 1.5rem;
      width: 60px;
      height: 60px;
      background: rgba(255,255,255,0.1);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      backdrop-filter: blur(10px);
    }

    .contact-details {
      h3 {
        font-size: 1.25rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
      }

      p {
        font-size: 1.1rem;
        opacity: 0.9;
      }
    }
  }
}

.contact-form {
  background: rgba(255,255,255,0.1);
  padding: 3rem;
  border-radius: 20px;
  backdrop-filter: blur(10px);

  h3 {
    font-size: 1.75rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #ffd700;
  }

  p {
    font-size: 1.1rem;
    line-height: 1.8;
    margin-bottom: 2rem;
    opacity: 0.9;
  }
}

.contact-button {
  background: linear-gradient(45deg, #ffd700, #ffed4e);
  color: #333;
  border: none;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 50px;
  cursor: pointer;
  transition: transform 0.3s ease, box-shadow 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(255,215,0,0.3);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .hero-content,
  .intro-content,
  .innovation-content,
  .contact-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .brand-name {
    font-size: 3rem;
  }

  .section-title {
    font-size: 2rem;
  }

  .timeline {
    &::before {
      left: 30px;
    }
  }

  .timeline-item {
    &.left,
    &.right {
      .timeline-content {
        margin-left: 60px;
        margin-right: 0;
        text-align: left;

        &::before {
          left: -30px;
          border-right-color: white;
          border-left-color: transparent;
        }
      }
    }
  }

  .timeline-dot {
    left: 30px;
  }

  .hero-stats {
    flex-direction: column;
    gap: 1rem;
  }

  .innovation-stats {
    grid-template-columns: 1fr;
  }

  .honors-grid {
    grid-template-columns: 1fr;
  }

  .products-grid {
    grid-template-columns: 1fr;
  }

  .gallery-grid {
    grid-template-columns: 1fr;
  }

  .video-placeholder {
    height: 250px;
  }

  .contact-form {
    padding: 2rem;
  }

  section {
    padding: 3rem 0;
  }

  .container {
    padding: 0 1rem;
  }
}
