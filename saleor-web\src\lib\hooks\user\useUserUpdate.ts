"use client"
import axios from "axios";
import { useEffect, useState } from "react";
import { message } from 'antd';

export function useUpdateCustomer() {
  const [customer, setCustomer] = useState();

  useEffect(() => {
    const fetchData = async () => {
      try {
        const res = await fetch("/api/auth/retrieve");
        const data = await res.json() as any;
        setCustomer(data);
      } catch (e: any) {
        // message.error(e.message || "Some errors have occurred");
      }
    };
    fetchData();
  }, []);

  async function updateCustomer(userInfoUdate: any) {
    try {
      const { avatar_url, ...rest } = userInfoUdate;
      const response = await axios.put(`/api/auth/update`, rest);
      return response.data;
    } catch (e: any) {
      message.error(e.message || "Some errors have occurred");
    }
  }

  async function updateCustomerBillingAddress(billingAddress: any) {
    try {
      const response = await axios.put(`/api/auth/update`, {
        billing: billingAddress
      });
      return response.data;
    } catch (e: any) {
      message.error(e.message || "Some errors have occurred");
    }
  }

  async function updateCustomerShippingAddress(shippingAddress: any) {
    try {
      const response = await axios.put(`/api/auth/update`, {
        shipping: shippingAddress
      });
      return response.data;
    } catch (e: any) {
      message.error(e.message || "Some errors have occurred");
      return Promise.reject(e);
    }
  }

  return { customer, updateCustomer, updateCustomerShippingAddress, updateCustomerBillingAddress };
}
