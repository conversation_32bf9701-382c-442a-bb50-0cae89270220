"use client";
import { fetchSearchProductsData } from "@/lib/api/product";
import { useLoveStore } from "@/lib/store/love.store";
import clsx from "clsx";
import { useLocale, useTranslations } from "next-intl";
import React, { useEffect, useState } from "react";
import { RingLoader } from "react-spinners";
import ProductCard from "../Product/product-card";
import EmptyState from "../EmptyState";
import { ProductListItemFragment } from "@/gql/graphql";
import Masonry from "react-masonry-css";
import { HomeTaile } from "../Contact/ConcatPage";
import CompareList from "../CompareList";
import MyEmpty from "../MyEmpty";
import { Button } from "../Button";
import { Checkbox, message } from "antd";
import { motion } from "framer-motion";
import { useShoppingCart } from "@/lib/hooks/useShoppingCart";
import { setInquiry } from "@/lib/utils/util";
import { useRouter } from "@/navigation";
export const fetchData = async (
	ids: number[],
	setLoading: (loading: boolean) => void,
	locale: string,
): Promise<any> => {
	try {
		setLoading(true);
		const { products } = await fetchSearchProductsData({ ids: ids, locale, channel: "default-channel" });
		console.log(products.edges, "productsproductsproducts");

		return products.edges;
	} finally {
		setLoading(false);
	}
};

function Index() {
	const [loveList, setLoveList] = useState([]);
	const t = useTranslations();
	const { loveIds, removeAllLove } = useLoveStore();
	const [loading, setLoading] = useState(false);
	const locale = useLocale();
	const router = useRouter();

	// 多选相关状态
	const [selectedItems, setSelectedItems] = useState<string[]>([]);
	const [isSelectMode, setIsSelectMode] = useState(false);
	const [isAllSelected, setIsAllSelected] = useState(false);
	const [batchLoading, setBatchLoading] = useState(false);

	// 购物车和询盘hooks
	const { addToCart } = useShoppingCart();

	useEffect(() => {
		if (!loveIds.length || !locale) return setLoveList([]);
		fetchData(loveIds, setLoading, locale).then((res) => {
			setLoveList(res);
		});
	}, [loveIds, locale]);

	// 监听选中项变化，更新全选状态
	useEffect(() => {
		if (loveList.length > 0) {
			setIsAllSelected(selectedItems.length === loveList.length);
		}
	}, [selectedItems, loveList]);

	const breakpointColumnsObj = {
		default: 4,
		1100: 3,
		768: 2
	};

	// 多选功能处理函数
	const handleSelectModeToggle = () => {
		setIsSelectMode(!isSelectMode);
		setSelectedItems([]);
		setIsAllSelected(false);
	};

	const handleSelectAll = () => {
		if (isAllSelected) {
			setSelectedItems([]);
		} else {
			const allIds = loveList.map((item: any) => item.node.id);
			setSelectedItems(allIds);
		}
	};

	const handleItemSelect = (productId: string) => {
		setSelectedItems(prev => {
			if (prev.includes(productId)) {
				return prev.filter(id => id !== productId);
			} else {
				return [...prev, productId];
			}
		});
	};

	// 获取产品的第一个变体
	const getFirstVariant = (product: any) => {
		return product.variants?.[0] || null;
	};

	// 批量加入购物车
	const handleBatchAddToCart = async () => {
		// if (selectedItems.length === 0) {
		// 	message.warning(t("common.Please_select_products"));
		// 	return;
		// }

		setBatchLoading(true);
		let successCount = 0;
		let failCount = 0;

		try {
			// 获取选中的产品
			const selectedProducts = loveList.filter((item: any) =>
				selectedItems.includes(item.node.id)
			);

			console.log("Selected products for cart:", selectedProducts);
			console.log("Selected items IDs:", selectedItems);

			// 逐个添加到购物车
			for (const item of selectedProducts) {
				const product = item.node;
				const firstVariant = getFirstVariant(product);

				console.log(`Product ${product.id}:`, {
					product: product,
					firstVariant: firstVariant,
					hasVariants: !!product.variants,
					variantsCount: product.variants?.edges?.length || 0
				});

				// 更宽松的检查条件 - 只要有变体ID就尝试添加
				if (firstVariant && firstVariant.id) {
					try {
						const result = await addToCart({
							channel: "default-channel",
							selectedVariantID: firstVariant.id,
							quantity: 1,
						});

						// 检查addToCart的返回值，如果返回false说明添加失败
						if (result !== false) {
							successCount++;
						} else {
							failCount++;
						}
					} catch (error) {
						console.error(`Failed to add product ${product.id} to cart:`, error);
						failCount++;
					}
				} else {
					console.warn(`Product ${product.id} has no valid variant`);
					failCount++;
				}
			}

			// 显示结果消息
			// if (successCount > 0) {
			// 	message.success(t("common.Added_to_cart_successfully") + ` (${successCount}/${selectedItems.length})`);
			// }
			// if (failCount > 0) {
			// 	message.warning(`${failCount} products could not be added to cart`);
			// }

		} catch (error) {
			console.error("Batch add to cart error:", error);
			message.error("Failed to add products to cart");
		} finally {
			setBatchLoading(false);
			// 重置选择状态
			setSelectedItems([]);
			setIsSelectMode(false);
		}
	};

	// 批量询盘
	const handleBatchInquiry = async () => {
		// if (selectedItems.length === 0) {
		// 	message.warning(t("common.Please_select_products"));
		// 	return;
		// }

		setBatchLoading(true);

		try {
			// 获取选中的产品
			const selectedProducts = loveList.filter((item: any) =>
				selectedItems.includes(item.node.id)
			);

			// 逐个添加到询盘
			for (const item of selectedProducts) {
				const product = item.node;
				const firstVariant = getFirstVariant(product);
				if (firstVariant) {
					try {
						setInquiry(product, 1, firstVariant.name || "Default Variant");
					} catch (error) {
						console.error(`Failed to add product ${product.id} to inquiry:`, error);
					}
				}
			}

			message.success(t("common.Inquiry_submitted_successfully"));

			// 跳转到询盘页面
			setTimeout(() => {
				router.push("/inquiry");
			}, 1000);

		} catch (error) {
			console.error("Batch inquiry error:", error);
			message.error("Failed to add products to inquiry");
		} finally {
			setBatchLoading(false);
			// 重置选择状态
			setSelectedItems([]);
			setIsSelectMode(false);
		}
	};


	return (
		<div>
			<HomeTaile msg={t("common.COLLECTION")} />
			<div className="container my-16">
				{!loading && !!loveList.length && (
					<div className="mb-6 space-y-4">
						{/* 顶部操作栏 */}
						<div className="flex flex-wrap items-center justify-between gap-4 rounded-lg bg-gray-50 p-4">
							<div className="flex items-center gap-4">
								<Button
									onClick={handleSelectModeToggle}
									className={clsx(
										"flex items-center gap-2 px-4 py-2 text-sm font-medium transition-all duration-200",
										isSelectMode
											? "bg-blue-600 text-white hover:bg-blue-700"
											: "bg-white border border-gray-300 hover:bg-gray-50 !text-black"
									)}
								>
									<i className={clsx("text-base", isSelectMode ? "ri-close-line" : "ri-checkbox-multiple-line")}></i>
									{isSelectMode ? t("common.Cancel_select") : t("common.Multi_select")}
								</Button>

								{isSelectMode && (
									<Checkbox
										checked={isAllSelected}
										onChange={handleSelectAll}
										className="text-sm"
									>
										{t("common.Select_all")} ({selectedItems.length}/{loveList.length})
									</Checkbox>
								)}
							</div>

							<div className="flex items-center gap-2">
								{!isSelectMode && (
									<div className="cursor-pointer text-sm duration-300 hover:text-main hover:underline" onClick={removeAllLove}>
										<i className="ri-delete-bin-6-line mr-1"></i>
										{t("common.2404e3fb11a4cd4619b87d75ba56e345d8b0")}
									</div>
								)}
							</div>
						</div>

						{/* 批量操作按钮 */}
						{isSelectMode && selectedItems.length > 0 && (
							<motion.div
								initial={{ opacity: 0, y: -10 }}
								animate={{ opacity: 1, y: 0 }}
								className="flex flex-wrap items-center gap-3 rounded-lg bg-blue-50 p-4 border border-blue-200"
							>
								{/* <span className="text-sm text-blue-700 font-medium">
									{t("common.Selected_items").replace("{count}", selectedItems.length.toString())}
								</span> */}
								<div className="flex gap-2">
									<Button
										onClick={handleBatchAddToCart}
										disabled={batchLoading}
										className="flex items-center gap-2 bg-mainColor text-white hover:bg-opacity-80 px-4 py-2 text-sm disabled:opacity-50 disabled:cursor-not-allowed"
									>
										{batchLoading ? (
											<RingLoader color="#fff" size={14} />
										) : (
											<i className="ri-shopping-cart-line"></i>
										)}
										{batchLoading ? t("common.Adding") : t("common.Add_to_cart")}
									</Button>
									<Button
										onClick={handleBatchInquiry}
										disabled={batchLoading}
										className="flex items-center gap-2 bg-black text-white hover:bg-opacity-80 px-4 py-2 text-sm disabled:opacity-50 disabled:cursor-not-allowed"
									>
										{batchLoading ? (
											<RingLoader color="#fff" size={14} />
										) : (
											<i className="ri-mail-line"></i>
										)}
										{batchLoading ? t("common.Processing") : t("common.Inquiry")}
									</Button>
								</div>
							</motion.div>
						)}
					</div>
				)}
				{loading ? (
					<div className="c-flex min-h-[50vh]">
						<RingLoader color="#000" />
					</div>
				) : loveList.length ? (
					<Masonry breakpointCols={breakpointColumnsObj}
						className="my-masonry-grid"
						columnClassName="my-masonry-grid_column">
						{loveList.map((item, index) => {
							const productId = item.node.id;
							const isSelected = selectedItems.includes(productId);

							return (
								<div key={index} className="relative">
									{/* 多选模式下的选择框 */}
									{isSelectMode && (
										<div className="absolute top-2 left-2 z-10">
											<Checkbox
												checked={isSelected}
												onChange={() => handleItemSelect(productId)}
												className=""
											/>
										</div>
									)}

									{/* 产品卡片 */}
									<div className={clsx(
										"transition-all duration-200",
										isSelectMode && isSelected && "ring-2 ring-blue-500 ring-offset-2"
									)}>
										<ProductCard
											productItem={item.node as ProductListItemFragment}
											locale={locale}
											isSelectMode={isSelectMode}
											isSelected={isSelected}
											onSelect={() => handleItemSelect(productId)}
										/>
									</div>
								</div>
							);
						})}
					</Masonry>
				) : (
					<MyEmpty text={t('nav.Wishlist')} description={t('nav.Youhave')} className="py-20 max-md:py-4">
						<i className="ri-heart-3-line text-3xl  !text-black"></i>
					</MyEmpty>
				)}
			</div>
			<div className="max-md:hidden">
				<CompareList />
			</div>
		</div>
	);
}

export default React.memo(Index);
