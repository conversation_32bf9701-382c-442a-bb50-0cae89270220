query ProductAllCategoriesList($locale: LanguageCodeEnum!, $first: Int, $after: String) {
	categories(first: $first, after: $after) {
		pageInfo {
			endCursor
			hasNextPage
		}
		edges {
			node {
				products {
					totalCount
				}
				parent {
					id
					name
					slug
				}
				...AllCategoryChildrenList
			}
		}
	}
}

fragment AllCategoryChildrenList on Category {
	...CategoryWithTranslation
}
