"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("src/middleware",{

/***/ "(middleware)/./src/middleware.ts":
/*!***************************!*\
  !*** ./src/middleware.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (/* binding */ middleware)\n/* harmony export */ });\n/* harmony import */ var next_intl_middleware__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-intl/middleware */ \"(middleware)/./node_modules/next-intl/dist/development/middleware.js\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./config */ \"(middleware)/./src/config.ts\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/server */ \"(middleware)/./node_modules/next/dist/esm/api/server.js\");\n\n\n\n/**\r\n * 获取用户IP地址\r\n */ function getClientIP(request) {\n    const forwarded = request.headers.get(\"x-forwarded-for\");\n    const realIP = request.headers.get(\"x-real-ip\");\n    const cfConnectingIP = request.headers.get(\"cf-connecting-ip\");\n    if (forwarded) {\n        return forwarded.split(\",\")[0].trim();\n    }\n    if (realIP) return realIP;\n    if (cfConnectingIP) return cfConnectingIP;\n    return request.ip || \"127.0.0.1\";\n}\n/**\r\n * 检查IP是否为中国IP\r\n */ function isChineseIP(ip) {\n    // 本地开发环境跳过检测\n    // if (ip === '127.0.0.1' || ip === '::1' || ip.startsWith('192.168.') || ip.startsWith('10.') || ip.startsWith('172.')) {\n    // \treturn false;\n    // }\n    const ipParts = ip.split(\".\").map(Number);\n    if (ipParts.length !== 4) return false;\n    // 中国主要运营商的IP段首字节\n    const chineseFirstOctets = [\n        1,\n        14,\n        27,\n        36,\n        39,\n        42,\n        49,\n        58,\n        59,\n        60,\n        61,\n        101,\n        103,\n        106,\n        110,\n        111,\n        112,\n        113,\n        114,\n        115,\n        116,\n        117,\n        118,\n        119,\n        120,\n        121,\n        122,\n        123,\n        124,\n        125,\n        180,\n        182,\n        183,\n        202,\n        203,\n        210,\n        211,\n        218,\n        219,\n        220,\n        221,\n        222,\n        223\n    ];\n    return chineseFirstOctets.includes(ipParts[0]);\n}\n// 定义一个默认导出的中间件函数，用于处理权限验证和路由跳转\nfunction middleware(request) {\n    const { pathname } = request.nextUrl;\n    // 检查是否为没有语言前缀的路径\n    const hasLocalePrefix = _config__WEBPACK_IMPORTED_MODULE_0__.locales.some((locale)=>pathname === `/${locale}` || pathname.startsWith(`/${locale}/`));\n    // 只对根路径进行IP检测重定向，避免覆盖用户的语言选择\n    if (pathname === \"/\" && !hasLocalePrefix) {\n        const clientIP = getClientIP(request);\n        // 只有检测到中国IP时才重定向到中文\n        if (isChineseIP(clientIP)) {\n            const url = request.nextUrl.clone();\n            url.pathname = `/zh-Hans${pathname}`;\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(url);\n        }\n    // 非中国IP不做处理，让next-intl处理默认重定向\n    }\n    // 创建下一个中间件实例 多语言中间件\n    const next = (0,next_intl_middleware__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n        locales: _config__WEBPACK_IMPORTED_MODULE_0__.locales,\n        defaultLocale: _config__WEBPACK_IMPORTED_MODULE_0__.defaultLocale,\n        pathnames: _config__WEBPACK_IMPORTED_MODULE_0__.pathnames,\n        localePrefix: _config__WEBPACK_IMPORTED_MODULE_0__.localePrefix,\n        localeDetection: false\n    });\n    // 其他情况，继续执行下一个中间件\n    return next(request);\n}\nconst config = {\n    matcher: [\n        // 在根目录中启用重定向到匹配的区域设置\n        \"/\",\n        // 设置cookie以记住以前的区域设置\n        // 所有具有区域设置前缀的请求\n        // `/(${locales.map(item => item).join(\"|\")})/:path*`,\n        \"/(af|am|ar|as|az|ba|bg|bho|bn|bo|brx|bs|ca|cs|cy|da|de|doi|dsb|dv|el|en|es|et|eu|fa|fi|fil|fj|fo|fr|fr-CA|ga|gl|gom|gu|ha|he|hi|hne|hr|hsb|ht|hu|hy|id|ig|ikt|is|it|iu|iu-Latn|ja|ka|kk|km|kmr|kn|ko|ks|ku|ky|ln|lo|lt|lug|lv|lzh|mai|mg|mi|mk|ml|mn-Cyrl|mn-Mong|mni|mr|ms|mt|mww|my|nb|ne|nl|nso|nya|or|otq|pa|pl|prs|ps|pt|pt-PT|ro|ru|run|rw|sd|si|sk|sl|sm|sn|so|sq|sr-Cyrl|sr-Latn|st|sv|sw|ta|te|th|ti|tk|tlh-Latn|tlh-Piqd|tn|to|tr|tt|ty|ug|uk|ur|uz|vi|xh|yo|yua|yue|zh-Hans|zh-Hant|zu)/:path*\",\n        // 启用添加缺失区域设置的重定向\n        //'(e.g. `/pathnames` -> `/en/pathnames`)',\n        \"/((?!api|_next|_vercel|.*\\\\..*).*)\"\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./src/middleware.ts\n");

/***/ })

});