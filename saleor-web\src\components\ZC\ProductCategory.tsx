"use client";
import SEOOptimizedImage from "@/components/Image/SEOOptimizedImage";
import { useTranslations } from "next-intl";
import { motion } from "framer-motion";
import { Link } from "@/navigation";
import { useMemo } from "react";
import { defaultLocale } from "@/config";
import { productTranslationName } from "@/lib/utils/util";
// 动画变体
const containerVariants = {
	hidden: { opacity: 0 },
	visible: {
		opacity: 1,
		transition: {
			staggerChildren: 0.1,
		},
	},
};

const itemVariants = {
	hidden: {
		opacity: 0,
		y: 30,
		scale: 0.95,
	},
	visible: {
		opacity: 1,
		y: 0,
		scale: 1,
		transition: {
			duration: 0.6,
			ease: [0.25, 0.46, 0.45, 0.94],
		},
	},
};

// 单个产品分类组件
const CategoryCard = ({ category, index }: { category: any; index: number }) => {
	const t = useTranslations("common");
	return (
		<div
			className="relative"
			//   variants={itemVariants}
			//   whileHover={{
			//     y: -8,
			//     transition: { duration: 0.3 }
			//   }}
		>
			{/* 产品图片区域 */}
			<SEOOptimizedImage
				src={category.image}
				alt={category.title}
				width={600}
				height={400}
				quality={100}
				priority
				className="h-auto w-full"
			/>
			{/* 标题和按钮区域 */}
			<div className=" absolute left-1/2 top-[40px] flex w-full -translate-x-1/2 flex-col items-center max-lg:top-[10px]">
				<h3 className="mb-4 text-lg font-bold tracking-wide text-gray-800 max-lg:mb-2 sm:text-xl lg:text-3xl uppercase">
					{category.title}
				</h3>

				<motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
					<Link
						href={category.link}
						className="inline-block rounded-full bg-mainColor px-6
              py-1 text-sm font-medium text-white shadow-lg transition-all duration-300 hover:bg-mainColor/80
              hover:text-white hover:shadow-xl sm:px-8 sm:py-2 sm:text-base"
					>
						{t("Learn_more")}
					</Link>
				</motion.div>
			</div>

			{/* 底部描述（可选） */}
			{/* <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent
        opacity-0 group-hover:opacity-100 transition-opacity duration-300 p-4">
                <p className="text-white text-sm text-center">
                    {category.description}
                </p>
            </div> */}
		</div>
	);
};

export default function ProductCategory({
	locale,
	categories,
	useStaticData = false,
}: {
	locale?: string;
	categories?: any[];
	useStaticData?: boolean;
}) {
	const t = useTranslations("");

	// 原始静态数据
	const staticProductCategories = [
		{
			id: 1,
			title: "MEETING TABLE",
			titleZh: "会议桌",
			image: "/image/home/<USER>",
			description: "专业会议桌，提升会议效率",
			link: "/products/meeting-table",
		},
		{
			id: 2,
			title: "COFFEE TABLE",
			titleZh: "咖啡桌",
			image: "/image/home/<USER>",
			description: "休闲咖啡桌，营造轻松氛围",
			link: "/products/coffee-table",
		},
		{
			id: 3,
			title: "STORAGE CABINET",
			titleZh: "储物柜",
			image: "/image/home/<USER>",
			description: "多功能储物解决方案",
			link: "/products/storage-cabinet",
		},
		{
			id: 4,
			title: "WORKSTATION SYSTEM",
			titleZh: "工作站系统",
			image: "/image/home/<USER>",
			description: "现代化办公工作站",
			link: "/products/workstation-system",
		},
		{
			id: 5,
			title: "MOVABLE WRITING BOARD",
			titleZh: "移动写字板",
			image: "/image/home/<USER>",
			description: "灵活移动的书写解决方案",
			link: "/products/writing-board",
		},
		{
			id: 6,
			title: "WORK LIGHT",
			titleZh: "工作灯",
			image: "/image/home/<USER>",
			description: "专业办公照明设备",
			link: "/products/work-light",
		},
	];

	// 过滤出slug为"office-system-furni-ture"的分类的children
	const officeSystemChildren = useMemo(() => {
		if (!(categories as any)?.edges?.length) return [];

		const officeSystemCategory = (categories as any).edges.find(
			(category) => category.node?.slug === "office-system-furni-ture",
		);

		return officeSystemCategory?.node?.children?.edges || staticProductCategories;
	}, [categories]);

	// 根据配置选择数据源
	const displayCategories = useMemo(() => {
		if (useStaticData) {
			return staticProductCategories;
		}

		return officeSystemChildren.map((child, index) => {
			// 解析media数据获取图片
			const media = child.node?.metadata?.find((m) => m.key === "media");
			const imageUrl = media ? JSON.parse(media.value)[0]?.url : "/image/default-image.webp";

			return {
				id: child.node?.id,
				title:
					locale === defaultLocale
						? productTranslationName(child.node.name)
						: productTranslationName(child.node.translation)
							? productTranslationName(child.node.translation.name)
							: productTranslationName(child.node.name),
				image: imageUrl,
				link: `/products/${child.node.slug}`,
			};
		});
	}, [useStaticData, officeSystemChildren]);

	console.log("useStaticData:", useStaticData);
	console.log("displayCategories:", displayCategories);

	return (
		<section className="bg-white pt-4">
			<div className="px-4">
				{/* 产品网格 */}
				<motion.div
					className="grid grid-cols-1 gap-4 lg:grid-cols-2"
					variants={containerVariants}
					initial="hidden"
					whileInView="visible"
					viewport={{ once: true, margin: "-100px" }}
				>
					{displayCategories.map((category, index) => (
						<CategoryCard key={category.id} category={category} index={index} />
					))}
				</motion.div>
			</div>
		</section>
	);
}
