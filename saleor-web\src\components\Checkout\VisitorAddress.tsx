"use client";
import { useUserAuth } from "@/lib/hooks/useUserAuth";
import { useLocale, useTranslations } from "next-intl";
import React, { useState } from "react";
import Modals from "@/components/Modals";
import clsx from "clsx";
import { Controller, useForm } from "react-hook-form";
import { GETAddresscountryArea } from "@/lib/api/user";
import { useUserStore } from "@/store/user.store";
import { isValidPhoneNumber, parsePhoneNumber } from "libphonenumber-js";
import PhoneInput from "react-phone-input-2";
import { RingLoader } from "react-spinners";
import axios from "axios";
import { header, shippingTitle } from "@/lib/utils/util";
export default function VisitorAddress({setactiveshipping}) {
	let [Address, setAddress] = useState(null);

	const [open, setOpen] = useState(false);

	const [countries, setcountries] = useState([]);
	//省
	const [countryArea, setcountryArea] = useState([]);

	const [userShipping, setUserShipping] = useState();
	const t = useTranslations();
	const {
		register,
		handleSubmit,
		setValue,
		reset,
		formState: { errors, isValid },
		getValues: getFormValues,
		control,
	} = useForm({
		mode: "onBlur",
	});

	let locale = useLocale();
	//获取国家
	async function getCountries() {
		let { data } = await axios.post(
			`${process.env.NEXT_PUBLIC_AI_URL}/api/v1/ml_web/get_translate_country_json?language_code=${locale}`,
			{
				header,
			},
		);
		if (data.code == 200) {
          // 使用 shift() 方法删除数组第一项
      const countryList = [...data.result.country];
      countryList.shift();
			setcountries(countryList);
		
		}
	}

  //打开
  function openModel(){
    setOpen(true);
    getCountries()
  }
	const handleChangeCountry = async (e: React.ChangeEvent<HTMLSelectElement>) => {
		const selectedCountry = e.target.value;

    // console.log(selectedCountry,'selectedCountry');
    
	

		// 只在需要时获取州/省列表
		const { addressValidationRules } = await GETAddresscountryArea(
			selectedCountry,
			null,
		);
    setValue("addressValidationRules", addressValidationRules);
		setcountryArea(addressValidationRules.countryAreaChoices);
	};

	//提交 创建or修改
	const onsubmit = async (data: any) => {
		let Formdata = { ...getFormValues() };
		Formdata.phone = `+${data.phone}`;

	// console.log(data,"data");
	
    setAddress(Formdata)
    setOpen(false);
    setactiveshipping(Formdata)

    
	};
	return (
		<div>
			<div
				onClick={openModel}
				className="relative box-border flex w-full cursor-pointer items-center justify-between rounded-lg border-[1px] border-main bg-white px-6 py-4 shadow-sm transition-all duration-300 hover:shadow-md"
			>
				{Address ? (
					<div className="flex flex-col gap-2">
						{/* 收件人姓名 */}
						<div className="flex items-center gap-2  ">
							<div className="text-lg font-semibold text-main">
								{Address.firstName} {Address.lastName}
							</div>
						</div>

						{/* 地址信息 */}
						<div className="text-sm text-gray-700 ">
							<div className="mb-2">
								{Address.companyName} {Address.streetAddress1}
							</div>
							<div className="mb-2">
								{[Address.city, Address.countryArea, Address.postalCode]
									.filter(Boolean)
									.join(", ")}{" "}
								{Address.country.country}
							</div>
						</div>

						{/* 电话号码 */}
						<div className="flex items-center gap-2 text-sm text-gray-600">
							<svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path
									strokeLinecap="round"
									strokeLinejoin="round"
									strokeWidth={2}
									d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
								/>
							</svg>
							{Address.phone}
						</div>
					</div>
				) : (
					<div className="py-2 text-gray-500">{t("nav.no default address is set")} not</div>
				)}

				<div className="flex items-center gap-2">
					{Address ? (
						<span className="text-sm font-medium text-main">{t("nav.change")}</span>
					) : (
						<span className="text-gray-500">
							<span className="text-sm font-medium text-main">{t("nav.Add address")} </span>
						</span>
					)}
				</div>
			</div>

			<Modals open={open} setOpen={setOpen} top="20vh">
				<div className="s-flex  mb-5">
					<h3 className="text-xl font-bold !text-black">{t(shippingTitle)}</h3>
				</div>
				<div className="z-50 w-[90vw] lg:w-[80vw] xl:w-[50vw]">
					<form onSubmit={handleSubmit(onsubmit)}>
						<div className={clsx("w-full gap-y-8")}>
							{/* First Name & Last Name */}
							<div className="grid gap-8 sm:flex sm:gap-5">
								{/* First Name */}
								<div className="mb-5 w-full sm:w-1/2">
									<div className="relative">
										<label
											className={`absolute -top-2 ${
												errors.firstName ? "text-red-400" : "text-[#85929E]"
											} left-3 bg-white text-xs`}
											htmlFor="firstName"
										>
											{t("form.f788c9b20398a5481f38c08cf68f2cb8c964")}
										</label>
										<input
											className={`w-full appearance-none rounded-md border px-5 py-3.5 ${
												errors.firstName ? "border-red-400" : "border-[#DDE6F5]"
											} focus:shadow-outline leading-tight text-gray-700 focus:shadow-lg focus:outline-none`}
											type="text"
											placeholder={t("form.f788c9b20398a5481f38c08cf68f2cb8c964")}
											id="firstName"
											{...register("firstName", {
												required: t("message.f5414f2236ca1a42db98da7c6b796fbbf035"),
											})}
										/>
									</div>
								</div>
								{/* Last Name */}
								<div className="mb-5 w-full  sm:w-1/2">
									<div className="relative">
										<label
											className={`absolute -top-2 ${
												errors.lastName ? "text-red-400" : "text-[#85929E]"
											} left-3 bg-white text-xs`}
											htmlFor="lastName"
										>
											{t("form.2bb382a16eab9d446ac91af90ce99c3cc7f8")}
										</label>
										<input
											className={`focus:shadow-outline w-full appearance-none rounded-md border px-5 py-3.5 leading-tight text-gray-700 focus:shadow-lg focus:outline-none ${
												errors.lastName ? "border-red-400" : "border-[#DDE6F5]"
											}`}
											type="text"
											placeholder={t("form.2bb382a16eab9d446ac91af90ce99c3cc7f8")}
											id="lastName"
											{...register("lastName", {
												required: t("message.8fb4bc206b4dd947543999d66757d14712e6"),
											})}
										/>
									</div>
								</div>
							</div>

							{/* companyName name & country */}
							<div className="mb-5 grid gap-8 sm:flex  sm:gap-5">
								{/* 国家 */}
								{/* Country  */}
								<div className="w-full sm:w-1/2">
									<div className="relative">
										<label
											className={`absolute -top-2 ${
												errors.country ? "text-red-400" : "text-[#85929E]"
											} left-3 bg-white text-xs`}
											htmlFor="country"
										>
											{t("checkout.eae6597b15706c40889b12521a6e903edff3")}
										</label>
										<select
											title={t("form.country")}
											className={`focus:shadow-outline w-full appearance-none rounded-md border px-5 py-3.5 leading-tight text-gray-700 focus:shadow-lg focus:outline-none ${
												errors.country ? "border-red-400" : "border-[#DDE6F5]"
											}`}
                      {...register("country", {
												required: t("message.562a4f23b5987749455830dffe3b68e567e2"),
											})}
											onChange={handleChangeCountry}
										>
											<option value="">{t("checkout.861db9266e3bf743b5c84bb5b8de3b67ebcc")}</option>
											{countries.map((item, index) => (
												<option key={index} value={item.isoCode}>
													{item.isoCode} {item.name}
												</option>
											))}
										</select>
									</div>
								</div>

								{/*省 countryArea */}

								<div className="w-full sm:w-1/2">
									<div className="relative">
										<label
											className={`absolute -top-2 z-[1] ${
												errors.countryArea ? "text-red-400" : "text-[#85929E]"
											} left-3 bg-white text-xs`}
											htmlFor="countryArea"
										>
											{t("checkout.1350af9875f65e46975b46c51798e07e4ec6")}
										</label>
										<select
											title={t("form.countryArea")}
											className={`focus:shadow-outline w-full appearance-none rounded-md border px-5 py-3.5 leading-tight text-gray-700 focus:shadow-lg focus:outline-none ${
												errors.country ? "border-red-400" : "border-[#DDE6F5]"
											}`}
											id="countryArea"
											{...register("countryArea")}
											// onChange={handleChangeCountry}
										>
											<option value="">{t("checkout.dc09d2d018ce2d462a3837538323e8b86ad8")}</option>
											{countryArea?.map((item, index) => (
												<option key={index} value={item.verbose}>
													{item.verbose}
												</option>
											))}
										</select>
									</div>
								</div>
							</div>

							{/* Town / City & District */}
							<div className="mb-5 grid gap-8 sm:flex  sm:gap-5">
								{/* Town / City */}
								<div className="w-full sm:w-1/2">
									<div className="relative">
										<label
											className={`absolute -top-2 ${
												errors.city ? "text-red-400" : "text-[#85929E]"
											} left-3 bg-white text-xs`}
											htmlFor="city"
										>
											{t("checkout.db41a8b095d4e045dec8e41ee7b29f79a6e7")}
										</label>
										<input
											className={`focus:shadow-outline w-full appearance-none rounded-md border px-5 py-3.5 leading-tight text-gray-700 focus:shadow-lg focus:outline-none ${
												errors.city ? "border-red-400" : "border-[#DDE6F5]"
											}`}
											type="text"
											placeholder={t("form.City")}
											id="city"
											{...register("city", {
												required: t("message.cc6e29b38f06b64001e8e9a6a1bf81a2d3ef"),
											})}
										/>
									</div>
								</div>

								{/* Company name */}
								<div className="w-full sm:w-1/2">
									<div className="relative">
										<label
											className="absolute -top-2 left-3 bg-white text-xs text-[#85929E]"
											htmlFor="companyName"
										>
											{t("checkout.50d8b5d2a6636b442d08250ad47532b73fd5")}
										</label>
										<input
											className="focus:shadow-outline w-full appearance-none rounded-md border border-[#DDE6F5] px-5 py-3.5 leading-tight text-gray-700 focus:shadow-lg focus:outline-none"
											type="text"
											placeholder={t("form.c0b9782f9dec7d44b00be190c883b25e35f0")}
											id="companyName"
											{...register("companyName")}
										/>
									</div>
								</div>
							</div>
							{/* Street address */}
							<div className="mb-5 grid gap-8 sm:flex  sm:gap-5">
								<div className="w-full sm:w-1/2">
									<div className="relative">
										<label
											className={`absolute -top-2 ${
												errors.streetAddress1 ? "text-red-400" : "text-[#85929E]"
											} left-3 bg-white text-xs`}
											htmlFor="streetAddress1"
										>
											{t("checkout.7db196092b824b40ca89655c15feadaf064e")}
										</label>
										<input
											className={`focus:shadow-outline w-full appearance-none rounded-md border px-5 py-3.5 leading-tight text-gray-700 focus:shadow-lg focus:outline-none ${
												errors.streetAddress1 ? "border-red-400" : "border-[#DDE6F5]"
											}`}
											type="text"
											placeholder={t("common.Address")}
											id="streetAddress1"
											{...register("streetAddress1", {
												required: t("message.c9183efb9d6f184d03390158cd782f83a406"),
											})}
										/>
									</div>
								</div>
{/* Postcode / ZIP & Phone */}
								<div className="w-full sm:w-1/2">
									<div className="relative">
										<label
											className={`absolute -top-2 ${
												errors.postalCode ? "text-red-400" : "text-[#85929E]"
											} left-3 bg-white text-xs`}
											htmlFor="postalCode"
										>
											{t("checkout.dc9ed9c83a3614477c18c92b64ea25203d52")}
										</label>
										<input
											className={`focus:shadow-outline w-full appearance-none rounded-md border px-5 py-3.5 leading-tight text-gray-700 focus:shadow-lg focus:outline-none ${
												errors.postalCode ? "border-red-400" : "border-[#DDE6F5]"
											}`}
											type="text"
											placeholder={t("checkout.postalCode")}
											id="postalCode"
											{...register("postalCode", {
												required: t("message.3b70d452b0ded6486e2a9f82f3db3aac0e75"),
											})}
										/>
									</div>
								</div>

							</div>
							
							<div className="grid gap-8 sm:flex sm:gap-5">

								{/* Phone */}
								<div className="w-full sm:w-1/2">
									<div className="relative">
										<label
											className={`absolute -top-2 ${
												errors.phone ? "text-red-400" : "text-[#85929E]"
											} left-3 z-10 bg-white text-xs`}
											htmlFor="phone"
										>
											{t("common.Phone")}
										</label>
										<Controller
											name="phone"
											control={control}
											rules={{
												required: t("message.6da2af7b8d5c5345f20988a3cbccd7cdfb8f"),
												validate: (value) => {
													try {
														// 如果电话号码不包含 '+' 号，添加它
														const phoneNumberWithPlus = `+${value}`;

														// 验证电话号码是否有效
														if (!isValidPhoneNumber(phoneNumberWithPlus)) {
															return t("common.invalidPhone");
														}

														// 获取电话号码的详细信息
														const phoneNumber = parsePhoneNumber(phoneNumberWithPlus);

														// 检查是否是可能的号码
														if (!phoneNumber.isPossible()) {
															return t("message.invalidPhoneLength");
														}

														return true;
													} catch (error) {
														return t("common.invalidPhone");
													}
												},
											}}
											render={({ field: { onChange, value } }) => (
												<PhoneInput
													country={"us"}
													enableSearch
													searchPlaceholder={t("common.searchCountry")}
													inputProps={{
														id: "phone",
														placeholder: t("checkout.78fb2d8b706a0d46bfb800f2d2c97e103e1e"),
													}}
													value={value}
													onChange={(phone, country: any) => {
														onChange(phone);
														// 如果需要，可以更新国家信息
														setUserShipping((prev: any) => ({
															...prev,
															countryCode: country.countryCode,
															dialCode: country.dialCode,
														}));
													}}
													containerClass={`${errors.phone ? "phone-input-error" : ""}`}
													inputStyle={{
														width: "100%",
														height: "48px", // 匹配原来的高度
														borderRadius: "0.375rem",
														borderColor: errors.phone ? "#f87171" : "#DDE6F5",
														backgroundColor: "white",
													}}
													buttonStyle={{
														borderRadius: "0.375rem 0 0 0.375rem",
														borderColor: errors.phone ? "#f87171" : "#DDE6F5",
														backgroundColor: "white",
													}}
													searchStyle={{
														width: "90%",
														margin: "0 auto",
													}}
													dropdownStyle={{
														width: "350px",
													}}
												/>
											)}
										/>
										{errors.phone && (
											<p className="mt-1 text-xs text-red-400">
												{t("message.6da2af7b8d5c5345f20988a3cbccd7cdfb8f")}
											</p>
										)}
									</div>
								</div>
{/* email*/}
								<div className="w-full sm:w-1/2">
									<div className="relative">
										<label
											className={`absolute -top-2 ${
												errors.email ? "text-red-400" : "text-[#85929E]"
											} left-3 bg-white text-xs`}
											htmlFor="email"
										>
											{t("checkout.email")}
										</label>
										<input
											className={`focus:shadow-outline w-full appearance-none rounded-md border px-5 py-3.5 leading-tight text-gray-700 focus:shadow-lg focus:outline-none ${
												errors.email ? "border-red-400" : "border-[#DDE6F5]"
											}`}
											type="text"
											placeholder={t("checkout.email")}
											id="email"
											{...register("email", {
												required: t("message.3b70d452b0ded6486e2a9f82f3db3aac0e75"),
											})}
										/>
									</div>
								</div>

							</div>
						</div>

						<div className="flex gap-3 max-md:mb-[60px]">
							{/* Order Button */}
							<button
								id="buy-button"
								type="submit"
								className={`"shadow-4xl cursor-pointer bg-main text-white hover:opacity-70"
								 font-Roboto mt-6 flex items-center justify-center gap-4 rounded-md px-7 py-4 text-base font-semibold capitalize transition-all duration-300 ease-in-out`}
							>
								{ t("form.89d6993324b64541c93976dbb5de7af086c0")}

							</button>
						</div>
					</form>
				</div>
			</Modals>
		</div>
	);
}
