import { useShoppingCartStore } from '@/lib/store/shoppingCart';
import { useTranslations } from 'next-intl';
import React from 'react';
import CartList from "../Product/ShoppingCart/new-cart-list";
import EmptyState from '../EmptyState';
import { useShoppingCart } from '@/lib/hooks/useShoppingCart';
function Index() {
    const { cartList } = useShoppingCartStore();
    const t = useTranslations();
  const {
    addToCart,
    findCheckout,
    findOrCreateCheckoutId,
    createCheckout,
    removeCartItem,
    updateLinesCount,
  } = useShoppingCart();
    	// 删除一行 购物车
	function handelupdateLinesCount(id: string) {
		removeCartItem([id]);
	}

	async function handelupdateCount(id: string, count: number){

    try {
			await updateLinesCount({
        checkoutId: cartList.id,
        lines: [
          {
            lineId: id, // 购物车行项ID
            quantity: count, // 新的数量
          },
        ],
      });
		} catch (error) {
			console.error('Update cart item failed:', error);
			throw error; // 抛出错误，让CartList组件知道更新失败
		}

	}
  return (
    <div>
      		<p>{`(${cartList?.lines?.length || 0} ${t("shop.items")})`}</p>

          <div className="py-4">
				<div className="flex flex-col gap-y-8">
					{cartList && cartList?.lines?.length > 0 ? (
						<>
							{cartList?.lines.map((product, index) => {
								return (
									<CartList
										key={index}
										product={product}
										isShowCount={true}
										count={product.quantity}
										handelupdateLinesCount={handelupdateLinesCount}
										handelupdateCount={handelupdateCount}
									/>
								);
							})}

							<div>
							</div>
						</>
					) : (
						<EmptyState />
					)}
				</div>
			</div>
      
    </div>
  );
}

export default React.memo(Index);