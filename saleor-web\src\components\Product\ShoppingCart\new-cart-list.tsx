"use client";
import { Placeholder } from "@/components/Placeholder";
import { BodyText } from "@/components/BodyText";
import { Link } from "@/navigation";
import Price from "@/components/Price/price";
import Calculator from "@/components/Calculator";
import React, { useEffect, useMemo, useState } from "react";
import { useShoppingCart } from "@/lib/hooks/useShoppingCart";
import { getImagesForProduct } from "../imageGallery";
import { useLocale, useTranslations } from "next-intl";
import { Button } from "@/components/Button";
import { defaultLocale } from "@/config";

function CartList({
	product,
	count = 1,
	isShowCount = false,
	close = true,
	showCol = false,
	handelupdateLinesCount,
	handelupdateCount,
}: {
	product: any;
	count: number;
	isShowCount?: boolean;
	close?: boolean;
	showCol?: boolean;
	handelupdateLinesCount: (id: string) => void;
	handelupdateCount: (id:string, count:number) =>Promise<void>;
}) {
	const {
		addToCart,
		findCheckout,
		findOrCreateCheckoutId,
		createCheckout,
		removeCartItem,
		updateLinesCount,
	} = useShoppingCart();
	const [countValue, setCountValue] = useState(count);
	const [isLoading, setIsLoading] = useState(false);
	let locale = useLocale();
	const t = useTranslations("shop");
	// product
	let [Reaproduct] = useState(product.variant.product);
	// variant
	let [Reavariant] = useState(product.variant);
	const imagesList = getImagesForProduct(Reaproduct) as any[];

	const changeCount = async (val: number) => {
		try {
			setIsLoading(true);
			setCountValue(val);
			await handelupdateCount(product.id, val);
		} catch (error) {
			// 如果更新失败，恢复原来的数量
			setCountValue(count);
			console.error('Failed to update quantity:', error);
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<>
			<div className="flex flex-col border bg-white p-4 shadow mb-4 rounded-sm">
				<>
					<div className="b-flex">
						<div className="s-flex">
							<Placeholder src={imagesList[0]?.url} imageHeight={130} imageWidth={100} />
							<div className="mx-4 flex flex-col">
								<Link href={`/product/${Reaproduct.slug}`}>
									<div className="line-clamp-1 text-black">
										{locale === defaultLocale ? Reaproduct.name : Reaproduct.translation.name||Reaproduct.name}
									</div>
								</Link>

								<div className="cursor-pointer mt-2">
									<span className={`font-SGL cursor-pointer !rounded-sm border-[1px] bg-white !px-4 !py-1 text-black shadow`}>
										{locale == defaultLocale ? Reavariant.name : Reavariant.translation.name||Reavariant.name}
									</span>
								</div>
							</div>
						</div>
						<div className="b-flex gap-x-4">
							<i
								className="ri-close-fill ri-xl cursor-pointer !text-black"
								onClick={() => handelupdateLinesCount(product.id)}
							></i>
						</div>
					</div>
					<ul className="space-y-4 max-md:space-y-2">
						<li className="b-flex text-themeSecondary600">
							<BodyText size="md">{t("Quantity")}</BodyText>
							{isShowCount ? (
								<div className="flex items-center">
									<Calculator
										initCount={product.quantity}
										className="h-[30px] max-md:max-w-[40px]"
										changeCount={changeCount}
										disabled={isLoading}
                    maxCount={Reavariant.quantityAvailable}
									/>

								</div>
							) : (
								<BodyText size="md" className="line-clamp-1">
									{countValue}
								</BodyText>
							)}
						</li>

						{process.env.NEXT_PUBLIC_SITE_TYPE == "toc" && (
							<li className="b-flex text-themeSecondary600">
								<BodyText size="md">{t("Subtotal")}</BodyText>
								<Price price={product.totalPrice.gross.amount} size="md" className="" />
							</li>
						)}
					</ul>
				</>
			</div>
		</>
	);
}

export default React.memo(CartList);
