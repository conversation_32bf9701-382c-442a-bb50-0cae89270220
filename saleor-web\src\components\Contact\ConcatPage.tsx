"use client"
import { contactInfo, contactObj } from '@/lib/contacts';
import { useTranslations } from 'next-intl';
import React from 'react'
import ContactForm from '../InquryForm/ContactForm';
import clsx from 'clsx';
import SEOOptimizedImage from '@/components/Image/SEOOptimizedImage';
import { Link } from "@/navigation";
import { motion } from 'framer-motion';
import Image from 'next/image';
import { YoutubeOutlined } from '@ant-design/icons'

export default function ConcatPage({ locale }: { locale: string }) {
  const t = useTranslations('nav');

  const navigation = {
    contact: [
      { title: t('Address'), value: `${contactInfo.address}`, hrefs: contactInfo.addressLink, icon: "ri-map-pin-fill", color: "text-blue-500" },
      { title: t('Phone'), value: `${contactInfo.phone}`, hrefs: `tel:${contactInfo.phone}`, icon: "ri-phone-fill", color: "text-green-500" },
      { title: t('Email'), value: `${contactInfo.email}`, hrefs: `mailto:${contactInfo.email}`, icon: "ri-mail-fill", color: "text-red-500" },
      { title: "WhatsApp", value: `${contactInfo.whatsapp}`, hrefs: `https://wa.me/${contactInfo.whatsapp.replace(/[^0-9]/g, '')}`, icon: "ri-whatsapp-fill", color: "text-green-600" },
      { title: "WeChat", value: `${contactInfo.wechat}`, hrefs: `weixin://dl/chat?${contactInfo.wechat}`, icon: "ri-wechat-fill", color: "text-green-600" },
    ],
    social: [
      { title: "Facebook", value: "Follow us on Facebook", hrefs: contactObj.FaceBook, icon: "ri-facebook-fill", color: "text-blue-600" },
      { title: "Instagram", value: "Follow us on Instagram", hrefs: contactObj.instagram, icon: "ri-instagram-fill", color: "text-pink-500" },
      { title: "YouTube", value: "Subscribe to our channel", hrefs: contactObj.Youtube, icon: "ri-youtube-fill", color: "text-red-600" },
      { title: "Pinterest", value: "Follow us on Pinterest", hrefs: contactObj.Pinterest, icon: "ri-pinterest-fill", color: "text-red-700" },
      { title: "LinkedIn", value: "Connect with us", hrefs: contactObj.Linkedin, icon: "ri-linkedin-fill", color: "text-blue-700" },
      // { title: "Twitter", value: "Follow us on Twitter", hrefs: contactObj.Twitter, icon: "ri-twitter-x-fill", color: "text-gray-900" },
      // { title: "TikTok", value: "Follow us on TikTok", hrefs: contactObj.Tiktok, icon: "ri-tiktok-fill", color: "text-gray-900" },
    ]
  };

  // 动画变体
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5
      }
    }
  };

  const slideInLeft = {
    hidden: { x: -100, opacity: 0 },
    visible: {
      x: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  };

  const slideInRight = {
    hidden: { x: 100, opacity: 0 },
    visible: {
      x: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  };

  return (
    <div className="bg-[#F2F4F5] min-h-screen">
      <HomeTaile msg={t("Contact Us")} />

      {/* 地图和信息面板 */}
      <div className='w-full bg-white'>
        <div className="max-w-7xl mx-auto grid grid-cols-1 lg:grid-cols-2 min-h-[600px] py-[64px] px-4">
          {/* 左侧地图 */}
          <motion.div
            className="w-full h-[400px] lg:h-full lg:flex lg:items-center"
            variants={slideInLeft}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
          >
            <iframe
              src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d7324.168603161255!2d113.39957914910808!3d23.385167363245117!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x34031e360f50c249%3A0xa6afe13c55180edf!2sZhongluotanzhen%2C%20Baiyun%2C%20Guangzhou%2C%20Guangdong%20Province%2C%20China%2C%20510550!5e0!3m2!1sen!2s!4v1753171302300!5m2!1sen!2s"
              width="100%"
              height="100%"
              style={{ border: 0 }}
              loading="lazy"
              referrerPolicy="no-referrer-when-downgrade"
            ></iframe>
          </motion.div>

          {/* 右侧信息面板 */}
          <motion.div
            className="bg-white p-8 lg:p-12 flex flex-col justify-center"
            variants={slideInRight}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
          >
            <motion.h3
              className="text-[#333] text-[28px] mb-8"
              variants={itemVariants}
            >
              {t('cf')}
            </motion.h3>

            {/* 联系方式 */}
            <motion.div
              className="space-y-4 mb-8"
              variants={containerVariants}
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true }}
            >
              {navigation.contact.map((item, index) => (
                <motion.div
                  key={index}
                  className="flex items-start space-x-4 p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-300"
                  variants={itemVariants}
                >
                  {/* 图标 */}
                  <div className={`flex-shrink-0 w-10 h-10 bg-white rounded-lg flex items-center justify-center shadow-sm`}>
                    <i className={`${item.color} text-lg ${item.icon}`}></i>
                  </div>

                  {/* 内容 */}
                  <div className="flex-1 min-w-0">
                    <div className="text-[#333] font-medium text-[16px] mb-1">{item.title}</div>
                    <Link
                      href={item.hrefs}
                      className="text-gray-600 text-[14px] hover:text-main transition-colors duration-300 break-words block"
                      target={'_blank'}
                    >
                      {item.value}
                    </Link>
                  </div>

                  {/* 跳转箭头 */}
                  <div className="flex-shrink-0">
                    <Link
                      href={item.hrefs}
                      target={'_blank'}
                      className="w-8 h-8 bg-main/10 hover:bg-main/20 rounded-full flex items-center justify-center transition-colors duration-300 group"
                    >
                      <i className="ri-external-link-line text-main text-sm group-hover:scale-110 transition-transform duration-300"></i>
                    </Link>
                  </div>
                </motion.div>
              ))}
            </motion.div>
          </motion.div>
        </div>

      </div>

      {/* 联系表单区域 */}
      <div
        className='w-full bg-[#f8f9fa] py-16 lg:py-20'
      >
        <div className="max-w-4xl mx-auto px-4 lg:px-8">
          <motion.div
            className="bg-white rounded-lg shadow-sm p-8 lg:p-12"
            initial={{ scale: 0.9, opacity: 0 }}
            whileInView={{ scale: 1, opacity: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <motion.h3
              className="text-[#333] font-semibold text-[28px] mb-4 text-center"
              initial={{ y: 20, opacity: 0 }}
              whileInView={{ y: 0, opacity: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.3 }}
            >
              {t('Get in Touch')}
            </motion.h3>
            <motion.p
              className="text-[#666] text-center mb-8 irs"
              initial={{ y: 20, opacity: 0 }}
              whileInView={{ y: 0, opacity: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.4 }}
            >
              {t('getInstantQuoteAndTS1')}
            </motion.p>

            <motion.div
              initial={{ y: 30, opacity: 0 }}
              whileInView={{ y: 0, opacity: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.5 }}
            >
              <ContactForm
                locale={locale}
                title=""
                className="!py-0"
                innerClassName="!shadow-none !rounded-none"
              />
              <div className="mt-4 text-xs text-gray-500">
                {t("Bysubmitting")}
                <Link href="/privacy-policy" className="text-main underline ml-1">
                  {t("privacyPolicy")}
                </Link>
              </div>
            </motion.div>

            {/* 社交媒体关注 */}
            <motion.div
              className="mt-12 pt-8 border-t border-gray-100"
              initial={{ y: 30, opacity: 0 }}
              whileInView={{ y: 0, opacity: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.6 }}
            >
              <h4 className="text-[#333] text-[20px] font-semibold mb-6 text-center">
                {t('fu')}
              </h4>
              <p className="text-[#666] text-center mb-8 text-sm">
                {t('sc')}
              </p>

              {/* 社交媒体图标网格 */}
              <motion.div
                className="flex flex-wrap justify-center gap-4"
                variants={containerVariants}
                initial="hidden"
                whileInView="visible"
                viewport={{ once: true }}
              >
                {navigation.social.map((item, index) => (
                  <motion.div
                    key={index}
                    variants={itemVariants}
                    className="group"
                  >
                    <Link
                      href={item.hrefs}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center justify-center w-12 h-12 bg-gray-100 hover:bg-gray-200 rounded-full transition-all duration-300 group-hover:scale-110 group-hover:shadow-lg"
                      title={item.title}
                    >
                      <i className={`${item.color} text-xl ${item.icon} group-hover:scale-110 transition-transform duration-300`}></i>
                    </Link>
                  </motion.div>
                ))}
              </motion.div>
            </motion.div>
          </motion.div>
        </div>
      </div>
    </div>
  )
}

export function HomeTaile({ msg }) {
  const t = useTranslations();
  return (
    <div className="py-[60px] px-[10px] bg-[#f5f5f7]">
      <div className="container max-md:mt-5">
        <h2 className="text-center font-bold text-[#3e454c] text-[20px]">{msg}</h2>
        <div className="flex mt-3 justify-center">
          <Link href={"/"} className="flex items-center justify-center hover:text-main text-[#3e454c] transition-all duration-300 ">
            {t("common.Home")}
          </Link>
          <span className="mx-2">/</span>
          <span className="text-[#3e454c]">{msg}</span>
        </div>
      </div>
    </div>
  )
}
