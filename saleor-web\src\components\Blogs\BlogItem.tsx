'use client'

import React from 'react'
import Image from 'next/image'
import { Link } from "@/navigation";
// import { BlogType } from '@/types/BlogType'
import * as Icon from "@phosphor-icons/react/dist/ssr";
import { useRouter } from "@/navigation";
import { type Blog } from "@/lib/@types/api/blog";
import { Tag } from "antd";
import moment from "moment/moment";

interface BlogProps {
    data: Blog.BlogListItem
}

const BlogItem: React.FC<BlogProps> = ({ data}) => {
    const router = useRouter()
    const handleBlogClick = (blogId: string) => {
        // Go to blog detail with blogId selected
        router.push(`/blog/detail1?id=${blogId}`);
    };

    return (
      <section className="container">
          <div
            className="blog-item style-list h-full cursor-pointer"
            onClick={() => handleBlogClick(data.blog_slug)}
          >
              <div className="blog-main h-full flex max-md:flex-col md:items-center md:gap-9 gap-6">
                  <div className="blog-thumb md:w-1/2 w-full rounded-[20px] overflow-hidden flex-shrink-0">
                      <Image
                        src={data.blog_image_origin}
                        width={550}
                        height={640}
                        alt='blog-img'
                        className='w-full h-full duration-500 flex-shrink-0 object-cover'
                      />
                  </div>
                  <div className="blog-infor">
                      <div
                        className="blog-tag bg-green py-1 px-2.5 rounded-full text-button-uppercase inline-block  gap-y-2">
                          {
                            data.blog_classification_list && data.blog_classification_list.map(item => {
                                return <Tag color="processing" key={item.cls_id} className="!mt-1">{item.cls_name}</Tag>
                            })
                          }
                      </div>
                      <div className="heading6 blog-title mt-3 duration-300">{data.blog_title}</div>
                      <div className="flex items-center gap-2 mt-2 !text-gray-500">
                          <div className="blog-author caption1 text-secondary">by {data.blog_author}</div>
                          <span className='w-[20px] h-[1px] bg-black'></span>
                          <div
                            className="blog-date caption1 text-secondary">{moment(data.blog_upload_time).format("MMM DD YYYY")}</div>
                      </div>
                      <div className="body1 text-secondary mt-4 text-gray-500">{data.blog_excerpt}</div>
                      <Link href={'/blog1/'+data.blog_slug} className="text-button underline mt-4 text-black">Read More</Link>
                  </div>
              </div>
          </div>
      </section>
    )
}

export default BlogItem
