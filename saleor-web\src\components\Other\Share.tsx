'use client'
import React from "react";
import {
	FacebookShare<PERSON>utton,
	TwitterShareButton,
	LinkedinShareButton,
	WhatsappShareButton,
} from "react-share";
import { <PERSON>edinLogo, WhatsappLogo } from "@phosphor-icons/react";
import { usePathname } from 'next/navigation';
import { useTranslations } from "next-intl";


export const SocialShare = () => {
	const pathname = usePathname(); // 当前路径，例如 /en/blog/123
	const fullUrl = `${process.env.NEXT_PUBLIC_SITE_URL}${pathname}`;
const t=useTranslations()
	return (
		<div className="flex gap-7">
			<div className="text-themeSecondary900">
				{t('base.ShareOn')}
			</div>
			<div className="flex gap-2">
				<FacebookShareButton url={fullUrl} className="!bg-gray-200 !text-black !w-8 !h-8  !rounded-full !duration-300 hover:!bg-black hover:!text-white">
					 	<div className="ri-facebook-fill duration-100"></div>
				</FacebookShareButton>
				<TwitterShareButton url={fullUrl} className="!bg-gray-200 !text-black !w-8 !h-8  !rounded-full !duration-300 hover:!bg-black hover:!text-white">
					<div className="ri-twitter-fill duration-100"></div>
				</TwitterShareButton>

				<LinkedinShareButton url={fullUrl}
														 className="!bg-gray-200 !text-black !w-8 !h-8  !rounded-full !duration-300 hover:!bg-black hover:!text-white flex justify-center items-center">
					<LinkedinLogo size={20} weight="fill" />				</LinkedinShareButton>
				<WhatsappShareButton url={fullUrl} className="!bg-gray-200 !text-black !w-8 !h-8  !rounded-full !duration-300 hover:!bg-black hover:!text-white flex justify-center items-center">
					<WhatsappLogo size={20} weight="fill" />
				</WhatsappShareButton>
			</div>
		</div>
	);
};


