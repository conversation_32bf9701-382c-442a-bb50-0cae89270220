"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("src/middleware",{

/***/ "(middleware)/./src/middleware.ts":
/*!***************************!*\
  !*** ./src/middleware.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (/* binding */ middleware)\n/* harmony export */ });\n/* harmony import */ var next_intl_middleware__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-intl/middleware */ \"(middleware)/./node_modules/next-intl/dist/development/middleware.js\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./config */ \"(middleware)/./src/config.ts\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/server */ \"(middleware)/./node_modules/next/dist/esm/api/server.js\");\n\n\n\n/**\r\n * 获取用户IP地址\r\n */ function getClientIP(request) {\n    const forwarded = request.headers.get(\"x-forwarded-for\");\n    const realIP = request.headers.get(\"x-real-ip\");\n    const cfConnectingIP = request.headers.get(\"cf-connecting-ip\");\n    if (forwarded) {\n        return forwarded.split(\",\")[0].trim();\n    }\n    if (realIP) return realIP;\n    if (cfConnectingIP) return cfConnectingIP;\n    return request.ip || \"127.0.0.1\";\n}\n/**\r\n * 检查IP是否为中国IP\r\n */ function isChineseIP(ip) {\n    // 本地开发环境跳过检测\n    // if (ip === '127.0.0.1' || ip === '::1' || ip.startsWith('192.168.') || ip.startsWith('10.') || ip.startsWith('172.')) {\n    // \treturn false;\n    // }\n    const ipParts = ip.split(\".\").map(Number);\n    if (ipParts.length !== 4) return false;\n    // 中国主要运营商的IP段首字节\n    const chineseFirstOctets = [\n        1,\n        14,\n        27,\n        36,\n        39,\n        42,\n        49,\n        58,\n        59,\n        60,\n        61,\n        101,\n        103,\n        106,\n        110,\n        111,\n        112,\n        113,\n        114,\n        115,\n        116,\n        117,\n        118,\n        119,\n        120,\n        121,\n        122,\n        123,\n        124,\n        125,\n        180,\n        182,\n        183,\n        202,\n        203,\n        210,\n        211,\n        218,\n        219,\n        220,\n        221,\n        222,\n        223\n    ];\n    return chineseFirstOctets.includes(ipParts[0]);\n}\n// 定义一个默认导出的中间件函数，用于处理权限验证和路由跳转\nfunction middleware(request) {\n    const { pathname } = request.nextUrl;\n    // 检查是否为没有语言前缀的路径\n    const hasLocalePrefix = _config__WEBPACK_IMPORTED_MODULE_0__.locales.some((locale)=>pathname === `/${locale}` || pathname.startsWith(`/${locale}/`));\n    // 如果没有语言前缀，进行IP检测\n    if (!hasLocalePrefix && !pathname.startsWith(\"/api\") && !pathname.startsWith(\"/_next\")) {\n        const clientIP = getClientIP(request);\n        // 检查用户是否已经有语言偏好设置（通过cookie）\n        const localeCookie = request.cookies.get(\"NEXT_LOCALE\");\n        const hasUserPreference = localeCookie && _config__WEBPACK_IMPORTED_MODULE_0__.locales.includes(localeCookie.value);\n        // 只有在没有用户偏好设置且检测到中国IP时才重定向到中文\n        if (!hasUserPreference && isChineseIP(clientIP)) {\n            const url = request.nextUrl.clone();\n            url.pathname = `/zh-Hans${pathname}`;\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(url);\n        }\n    // 如果有用户偏好或非中国IP，让next-intl处理默认重定向\n    }\n    // 创建下一个中间件实例 多语言中间件\n    const next = (0,next_intl_middleware__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n        locales: _config__WEBPACK_IMPORTED_MODULE_0__.locales,\n        defaultLocale: _config__WEBPACK_IMPORTED_MODULE_0__.defaultLocale,\n        pathnames: _config__WEBPACK_IMPORTED_MODULE_0__.pathnames,\n        localePrefix: _config__WEBPACK_IMPORTED_MODULE_0__.localePrefix,\n        localeDetection: false\n    });\n    // 其他情况，继续执行下一个中间件\n    return next(request);\n}\nconst config = {\n    matcher: [\n        // 在根目录中启用重定向到匹配的区域设置\n        \"/\",\n        // 设置cookie以记住以前的区域设置\n        // 所有具有区域设置前缀的请求\n        // `/(${locales.map(item => item).join(\"|\")})/:path*`,\n        \"/(af|am|ar|as|az|ba|bg|bho|bn|bo|brx|bs|ca|cs|cy|da|de|doi|dsb|dv|el|en|es|et|eu|fa|fi|fil|fj|fo|fr|fr-CA|ga|gl|gom|gu|ha|he|hi|hne|hr|hsb|ht|hu|hy|id|ig|ikt|is|it|iu|iu-Latn|ja|ka|kk|km|kmr|kn|ko|ks|ku|ky|ln|lo|lt|lug|lv|lzh|mai|mg|mi|mk|ml|mn-Cyrl|mn-Mong|mni|mr|ms|mt|mww|my|nb|ne|nl|nso|nya|or|otq|pa|pl|prs|ps|pt|pt-PT|ro|ru|run|rw|sd|si|sk|sl|sm|sn|so|sq|sr-Cyrl|sr-Latn|st|sv|sw|ta|te|th|ti|tk|tlh-Latn|tlh-Piqd|tn|to|tr|tt|ty|ug|uk|ur|uz|vi|xh|yo|yua|yue|zh-Hans|zh-Hant|zu)/:path*\",\n        // 启用添加缺失区域设置的重定向\n        //'(e.g. `/pathnames` -> `/en/pathnames`)',\n        \"/((?!api|_next|_vercel|.*\\\\..*).*)\"\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./src/middleware.ts\n");

/***/ })

});