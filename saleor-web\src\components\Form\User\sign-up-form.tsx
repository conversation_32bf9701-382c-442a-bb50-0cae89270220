"use client";
import React, { useState } from "react";
import { Button } from "@/components/Button";
import { RingLoader } from "react-spinners";
import { SubmitHand<PERSON>, useForm, Controller } from "react-hook-form";
import { useLocale, useTranslations } from "next-intl";
import { useCreateUser } from "@/lib/hooks/user/useCreateUser";
import PhoneInput from "react-phone-input-2";
import "react-phone-input-2/lib/style.css";

type FormValues = {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  position?: string;
  companyName?: string;
  password: string;
  confirmPassword: string;
  onSubmit: (data?: React.BaseSyntheticEvent<object, any, any> | undefined) => Promise<void>;
  locale?: string;
};

// 邮箱验证正则
const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

interface SignUpFormProps {
  setActive: any;
}

const SignUpForm = ({ setActive }: SignUpFormProps) => {
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
    control
  } = useForm<FormValues>();

  const { createUser, loading } = useCreateUser();
  const locale = useLocale();
  const t = useTranslations();

  // 监听密码字段用于确认密码验证
  const password = watch("password");

  const onSubmit: SubmitHandler<FormValues> = (data) => {
    data.locale = locale;
    // PhoneInput 已经包含了国家代码，不需要额外处理
    createUser(data, reset, setActive);
  };

  return (
    <div className="w-full">
      <div className="mt-1 md:mt-4 user">
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-2 md:space-y-4">
          {/* 姓名字段 - 移动端并排以节省空间 */}
          <div className="flex gap-2 md:gap-3">
            <div className="flex-1">
              <label htmlFor="firstName" className="block text-left text-xs md:text-sm font-medium text-gray-700 mb-1 md:mb-1.5">
                {t("resource.First Name")} <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="firstName"
                className={`w-full px-2 py-1.5 md:px-3 md:py-2 text-xs md:text-sm placeholder:text-xs md:placeholder:text-sm placeholder:text-gray-300
                  border rounded-sm outline-none bg-white text-black transition-colors duration-200
                  focus:ring-1 md:focus:ring-2 focus:ring-themePrimary500 focus:border-transparent
                  ${errors.firstName ? "border-red-500" : "border-themeSecondary300"}`}
                placeholder={t("resource.First Name")}
                {...register("firstName", {
                  required: t("common.First_Name_Required"),
                  minLength: { value: 2, message: t("common.First_Name_Min_Length") }
                })}
              />
              {errors.firstName && (
                <p className="mt-0.5 text-xs text-red-500">{errors.firstName.message}</p>
              )}
            </div>
            <div className="flex-1">
              <label htmlFor="lastName" className="block text-left text-xs md:text-sm font-medium text-gray-700 mb-1 md:mb-1.5">
                {t("resource.Last Name")} <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="lastName"
                className={`w-full px-2 py-1.5 md:px-3 md:py-2 text-xs md:text-sm placeholder:text-xs md:placeholder:text-sm placeholder:text-gray-300
                  border rounded-sm outline-none bg-white text-black transition-colors duration-200
                  focus:ring-1 md:focus:ring-2 focus:ring-themePrimary500 focus:border-transparent
                  ${errors.lastName ? "border-red-500" : "border-themeSecondary300"}`}
                placeholder={t("resource.Last Name")}
                {...register("lastName", {
                  required: t("common.Last_Name_Required"),
                  minLength: { value: 2, message: t("common.Last_Name_Min_Length") }
                })}
              />
              {errors.lastName && (
                <p className="mt-0.5 text-xs text-red-500">{errors.lastName.message}</p>
              )}
            </div>
          </div>
          {/* 邮箱字段 */}
          <div>
            <label htmlFor="email" className="block text-left text-xs md:text-sm font-medium text-gray-700 mb-1 md:mb-1.5">
              {t("common.Email")} <span className="text-red-500">*</span>
            </label>
            <input
              type="email"
              id="email"
              className={`w-full px-2 py-1.5 md:px-3 md:py-2 text-xs md:text-sm placeholder:text-xs md:placeholder:text-sm placeholder:text-gray-300
                border rounded-sm outline-none bg-white text-black transition-colors duration-200
                focus:ring-1 md:focus:ring-2 focus:ring-themePrimary500 focus:border-transparent
                ${errors.email ? "border-red-500" : "border-themeSecondary300"}`}
              placeholder={t("common.Email_Placeholder")}
              {...register("email", {
                required: t("common.Email_Required"),
                pattern: {
                  value: emailRegex,
                  message: t("common.Email_Invalid")
                }
              })}
            />
            {errors.email && (
              <p className="mt-0.5 text-xs text-red-500">{errors.email.message}</p>
            )}
          </div>

          {/* 手机号字段 */}
          <div>
            <label htmlFor="phone" className="block text-left text-xs md:text-sm font-medium text-gray-700 mb-1 md:mb-1.5">
              {t("form.phone")} <span className="text-red-500">*</span>
            </label>
            <Controller
              name="phone"
              control={control}
              rules={{
                required: t("common.Phone_Required"),
                validate: (value) => {
                  if (!value || value.length < 10) {
                    return t("common.Phone_Invalid");
                  }
                  return true;
                }
              }}
              render={({ field: { onChange, value } }) => (
                <div className="phone-input-container signup-phone-input">
                  <PhoneInput
                    country={"us"}
                    enableSearch
                    value={value}
                    onChange={onChange}
                    inputStyle={{
                      width: "100%",
                      // height: window.innerWidth < 768 ? "28px" : "36px", // 缩小PC端高度
                      // fontSize: window.innerWidth < 768 ? "12px" : "14px", // 缩小PC端字体
                      borderRadius: "0.125rem",
                      borderColor: errors.phone ? "#ef4444" : "#d4d4d8",
                      // paddingLeft: window.innerWidth < 768 ? "42px" : "44px", // 缩小PC端左边距
                      color: "#000000",
                      backgroundColor: "#ffffff",
                      textAlign: "left",
                    }}
                    buttonStyle={{
                      // height: window.innerWidth < 768 ? "28px" : "36px", // 缩小PC端高度
                      borderRadius: "0.125rem 0 0 0.125rem",
                      borderColor: errors.phone ? "#ef4444" : "#d4d4d8",
                      // width: window.innerWidth < 768 ? "36px" : "40px", // 缩小PC端宽度
                    }}
                    containerStyle={{
                      width: "100%",
                    }}
                    inputClass={`${errors.phone ? "!border-red-500" : "!border-themeSecondary300"}`}
                    buttonClass={`${errors.phone ? "!border-red-500" : "!border-themeSecondary300"}`}
                  />
                </div>
              )}
            />
            {errors.phone && (
              <p className="mt-0.5 text-xs text-red-500">{errors.phone.message}</p>
            )}
          </div>
          {/* 职位和公司字段 - 移动端并排以节省空间 */}
          <div className="flex gap-2 md:gap-3">
            <div className="flex-1">
              <label htmlFor="position" className="block text-left text-xs md:text-sm font-medium text-gray-700 mb-1 md:mb-1.5">
                {t("common.Position")}
              </label>
              <input
                type="text"
                id="position"
                className={`w-full px-2 py-1.5 md:px-3 md:py-2 text-xs md:text-sm placeholder:text-xs md:placeholder:text-sm placeholder:text-gray-300
                  border rounded-sm outline-none bg-white text-black transition-colors duration-200
                  focus:ring-1 md:focus:ring-2 focus:ring-themePrimary500 focus:border-transparent
                  ${errors.position ? "border-red-500" : "border-themeSecondary300"}`}
                placeholder={t("common.Position")}
                {...register("position")}
              />
            </div>
            <div className="flex-1">
              <label htmlFor="companyName" className="block text-left text-xs md:text-sm font-medium text-gray-700 mb-1 md:mb-1.5">
                {t("common.Company Name")}
              </label>
              <input
                type="text"
                id="companyName"
                className={`w-full px-2 py-1.5 md:px-3 md:py-2 text-xs md:text-sm placeholder:text-xs md:placeholder:text-sm placeholder:text-gray-300
                  border rounded-sm outline-none bg-white text-black transition-colors duration-200
                  focus:ring-1 md:focus:ring-2 focus:ring-themePrimary500 focus:border-transparent
                  ${errors.companyName ? "border-red-500" : "border-themeSecondary300"}`}
                placeholder={t("common.Company Name")}
                {...register("companyName")}
              />
            </div>
          </div>
          {/* 密码字段 - 移动端并排以节省空间 */}
          <div className="flex gap-2 md:block md:space-y-4">
            <div className="flex-1 md:w-full">
              <label htmlFor="password" className="block text-left text-xs md:text-sm font-medium text-gray-700 mb-1 md:mb-1.5">
                {t("common.Password")} <span className="text-red-500">*</span>
              </label>
              <input
                type="password"
                id="password"
                className={`w-full px-2 py-1.5 md:px-3 md:py-2 text-xs md:text-sm placeholder:text-xs md:placeholder:text-sm placeholder:text-gray-300
                  border rounded-sm outline-none bg-white text-black transition-colors duration-200
                  focus:ring-1 md:focus:ring-2 focus:ring-themePrimary500 focus:border-transparent
                  ${errors.password ? "border-red-500" : "border-themeSecondary300"}`}
                placeholder={t("common.Password_Placeholder")}
                {...register("password", {
                  required: t("common.Password_Required")
                })}
              />
              {errors.password && (
                <p className="mt-0.5 text-xs text-red-500">{errors.password.message}</p>
              )}
            </div>

            <div className="flex-1 md:w-full">
              <label htmlFor="confirmPassword" className="block text-left text-xs md:text-sm font-medium text-gray-700 mb-1 md:mb-1.5">
                {t("common.Confirm_Password")} <span className="text-red-500">*</span>
              </label>
              <input
                type="password"
                id="confirmPassword"
                className={`w-full px-2 py-1.5 md:px-3 md:py-2 text-xs md:text-sm placeholder:text-xs md:placeholder:text-sm placeholder:text-gray-300
                  border rounded-sm outline-none bg-white text-black transition-colors duration-200
                  focus:ring-1 md:focus:ring-2 focus:ring-themePrimary500 focus:border-transparent
                  ${errors.confirmPassword ? "border-red-500" : "border-themeSecondary300"}`}
                placeholder={t("common.Confirm_Password_Placeholder")}
                {...register("confirmPassword", {
                  required: t("common.Confirm_Password_Required"),
                  validate: value => value === password || t("common.Passwords_Not_Match")
                })}
              />
              {errors.confirmPassword && (
                <p className="mt-0.5 text-xs text-red-500">{errors.confirmPassword.message}</p>
              )}
            </div>
          </div>


          {/* 提交按钮 */}
          <div className="pt-1 md:pt-3">
            <Button
              disabled={loading}
              className={`flex gap-2 md:gap-3 items-center justify-center w-full max-md:py-2 md:py-3 text-sm font-medium
                bg-black text-white rounded-sm transition-all duration-200 hover:bg-gray-800
                disabled:opacity-50 disabled:cursor-not-allowed
                ${loading ? "bg-themeSecondary800" : ""}`}
            >
              {loading && <RingLoader color="#fff" size={14} />}
              {loading ? t("common.Registering") : t("menu.register")}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default SignUpForm;
