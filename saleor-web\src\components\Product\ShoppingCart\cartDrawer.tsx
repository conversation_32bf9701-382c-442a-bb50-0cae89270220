"use client";
import { ReactNode, useEffect, useState } from "react";
import { useTranslations } from "next-intl";

import { useUserStore } from "@/lib/store/user";
import  CartContent  from "./cartContent";
import {useRouter  } from "@/navigation";
import React from "react";
import { CartDrawerOpen } from "@/lib/store/CartDrawerOpen";
import { Drawer } from "antd";
import { CloseOutlined } from "@ant-design/icons";
import useIsMobile from "@/lib/hooks/useIsMobile";

type Props = {
  children: ReactNode,
  callback?: (...arg: any) => void,
  className?: string
}

 const CartDrawer = (props: Props) => {
  // const [open, setOpen] = useState(true);
  let {open, setOpen}= CartDrawerOpen()


  const { userInfo } = useUserStore();
  const t = useTranslations();
  const router = useRouter();
  const handlerOpen = () => {
    // if (!userInfo) {
    //   const login = document.querySelector("#web-login") as HTMLLIElement;
    //   login.click();
    //   return;
    // }
    props?.callback?.(true);
    setOpen(true);
  };
  useEffect(() => {
    setOpen(false);
  }, [router]);

 let isMobile= useIsMobile()

  return <>
    <div onClick={handlerOpen} className={props?.className ? props.className : ""}>
      {props.children}
    </div>
    <Drawer       // 移除默认关闭按钮
        closeIcon={null} width={isMobile?"86vw":'30vw'} className=" overflow-hidden cursor-auto " open={open}   placement={'right'}
            onClose={() => setOpen(false)}         // 自定义标题，将关闭按钮移到右侧
            title={
              <div className="flex justify-between items-center w-full">
                <span>{t("shop.0b1dcf5c5c3d9b4fe53b771d965840f7159e")}</span>

                <i  onClick={() => setOpen(false)} className="ri-close-line text-3xl text-black cursor-pointer"></i>
              </div>
            }>
      <div className="">
      {/* @ts-ignore */}
        <CartContent layout="col" />
      </div>
    </Drawer>
  </>;
};


export default React.memo(CartDrawer);