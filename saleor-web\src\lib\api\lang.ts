import { SITE_LOCALES } from "@/lib/constant";
import { type TBaseResponse } from "@/lib/@types/base";
import { type TLocale } from "@/lib/@types/locale";
import { defaultLocale } from "@/config";
import path from "path";
import fs from "fs";
import { returnLangs } from "@/lib/lang";
export const getLang = async (locale: string) => {
	// const defaultLang = (await import(`@/lib/lang/en.json`)).default;
const defaultLang=returnLangs()

	try {
		if (!locale || locale === defaultLocale) {
			return defaultLang;
		}else{
			return (await import(`@/lib/otherLang/${locale}.json`)).default;
		}
		//切片获取
	// 	const msg={}
 // return  await  fetchAllData(locale,1,msg)

	} catch (e) {
		console.error('多语言翻译文本获取失败',locale,e);
		return defaultLang;
	}
};
export async function fetchAllData(locale:string, page:number, msg :any) {
	const res = await get_dict(locale, page); // 获取当前页数据
	if (res ) {
		msg=Object.assign(msg,res.data)
	}
	if(res&&res.total>page){
		page++
		await fetchAllData(locale,page,msg)
	}
	// 如果达到最后一页，返回完整结果
	return msg;
}

export const  get_dict=async (locale:string,page:number)=>{
	try{
		const res = await fetch(`${process.env.NEXT_PUBLIC_AI_URL}/api/v1/ml_web/get_translate_cursor_dict`, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json', // 指定请求体的格式为JSON
			},
			body: JSON.stringify({
				language: locale,
				key_list:[],
				domain:new URL(process.env.NEXT_PUBLIC_SITE_URL||"").hostname,
				page: page,
				max_size_kb: 200 //切片大小300kb
			})
		}).then((r) => r.json() as any);
		if(res.code===200){
			return res.result
		}else{
			return null
		}
	}catch (e){
		return  null
	}


}

// 获取指定语言的语言包
export const getAppointLang = async (locale: string) => {
	const getLocalesUrl = `${
		process.env.NEXT_PUBLIC_TRANSLATE_URL
	}/api/languages?flag=1&codes=${SITE_LOCALES.map((item) => item.code).join(",")}`;
	return (await fetch(getLocalesUrl, {
		headers: {
			"Accept-Language": locale,
			"Content-Type": "application/json",
		},
	}).then((r) => r.json())) as TBaseResponse<TLocale[]>;
};
