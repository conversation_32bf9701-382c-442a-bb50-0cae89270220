import { NextApiRequest, NextApiResponse } from "next";
import { withXMLResponse } from "@/lib/utils/withXMLResponse";
import { cApiUrl, createSitemap, defaultLocale } from "@/lib/utils/util";
import { locales } from "@/config";
import { getBlogCategoriesLinks, getBlogLinks,  getIsEditMenus, getProductCategoriesLinks, getProductLinks } from "@/lib/utils/sitemap-util";
import { NextRequest, NextResponse } from 'next/server'
export const GET= async (req: Request)=> {
  if (process.env.NEXT_PUBLIC_IS_I18N == "false") {
    return new Response("not i18n!");
  }
  const url = new URL(req?.url || `${cApiUrl}/api/sitemap/${defaultLocale}?lang=${defaultLocale}`);
  // 从 URL 路径中获取语言代码
  const pathname = new URL(req.url).pathname;
  const locale = pathname.split('/').pop() || defaultLocale;
  
  const find = locales.find(item => item== locale);
  if (!find) {
    return new Response("Wrong path!");
  }
      const promise = [
        getIsEditMenus(),
        getProductLinks(),
        getBlogLinks(),
       getProductCategoriesLinks(),
       getBlogCategoriesLinks()
      ];
  const result = await Promise.all(promise);
  const allMap = result.flat().map(m => {
    const url = new URL(m);
    const origin = url.origin;
    const asPath = m.split(origin);
    asPath[0] = `${origin}/${locale}`;
    return asPath.join("");
  });

  return new NextResponse(createSitemap(allMap), {
    headers: {
      'Content-Type': 'application/xml',
    } ,
  }) as any
}
