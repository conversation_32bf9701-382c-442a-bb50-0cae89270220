import { type Metadata } from "next";
import React from "react";
import { getBasePageSeo } from "@/lib/api/seo";
import { generateSeo } from "@/lib/utils/seo";
import { type MyPageProps } from "@/lib/@types/base";
import Image from "next/image";
import { useTranslations } from "next-intl";
import { getTranslations,unstable_setRequestLocale } from "next-intl/server";
import { CountUpNumber } from "@/components/CountUpNumber/page";
import { Tracing } from "@/components/AboutUs/TracingBeam";
import AboutPage from "@/components/AboutPage";
import SignUpForm from "@/components/Form/User/sign-up-form";

/**
 * 生成页面的元数据
 * @param props 页面的属性，用于生成SEO信息
 * @returns 返回页面的元数据，包括SEO信息
 */
export const generateMetadata = async (props: MyPageProps): Promise<Metadata> => {
    props.params.page = ["register"];
    // 获取基础页面的SEO数据
    const seo = await getBasePageSeo(props);
    // 生成最终的SEO信息，并返回
    return generateSeo(props, {
        ...seo,
        ogType: "website",
    });
};








export default async function Register(props: MyPageProps) {
    unstable_setRequestLocale(props.params.locale);
    const t = await getTranslations();
    return (
        <>
            <div className="max-w-[600px] px-4 mx-auto py-28">
                <h2 className="max-md:text-2xl text-3xl font-bold text-center mb-10">{t("menu.register")}</h2>
                <SignUpForm setActive={2} />
            </div>
        </>
    );
}
