import { create } from "zustand";
import { persist } from "zustand/middleware";

export type InquiryItem = {
	quantity: number;
	productId: string;
	type: "main" | "variant";
};

type State = {
	inquiry: InquiryItem[];
	addInquiryItem: (item: InquiryItem) => void;
	removeInquiryItem: (id: string) => void;
	setInquiryItemQuantity: (id: string, quantity: number) => void;
};

export const useInquiryStore = create(
	persist<State>(
		(set, get) => ({
			inquiry: [],
			addInquiryItem: (item) => {
				const inquiry = get().inquiry;
				const id = item.productId;
				// 找到inquiry中id为item.productIds.id的元素
				const index = inquiry.findIndex((i) => i.productId === id);
				if (index !== -1) {
					// 如果找到元素，则更新其quantity
					set({
						inquiry: [
							...inquiry.slice(0, index),
							{
								...inquiry[index],
								quantity: item.quantity,
							},
							...inquiry.slice(index + 1),
						],
					});
				} else {
					// 如果没有找到元素，则添加新的元素
					set({ inquiry: [...inquiry, item] });
				}
			},
			removeInquiryItem: (id) => {
				set({ inquiry: get().inquiry.filter((i) => i.productId !== id) });
			},
			setInquiryItemQuantity: (id, quantity) => {
				const index = get().inquiry.findIndex((i) => i.productId === id);
				if (index === -1) return;
				set({
					inquiry: get().inquiry.map((i) => {
						if (i.productId === id) {
							return {
								...i,
								quantity,
							};
						}
						return i;
					}),
				});
			},
		}),
		{
			name: "inquiry-store",
		},
	),
);
