import { Button } from "@/components/Button";
import { useShoppingCart } from "@/lib/hooks/useShoppingCart";
import { setInquiry } from "@/lib/utils/util";
import { useUserStore } from "@/store/user.store";
import { message } from "antd";
import clsx from "clsx";
import { useTranslations } from "next-intl";
import { useState } from "react";
import { RingLoader } from "react-spinners";
type Props = {
	size?: "xs" | "sm" | "md" | "lg" | "xl";
	className?: string;
	count: number;
	id: string;
	text?: string;
	VariantActive: Object | null;
	product: Object | null;
  loading: boolean;
  callback:() => any;
};
export default function InquiryButton({
	size = "md",
	className = "",
	count,
	id,
	text = "form.Inquiry Now",
	VariantActive,
	product,
  loading,
  callback
}: Props) {
	const { addToCart } = useShoppingCart();
	let { userInfo } = useUserStore();
	const t = useTranslations();


	return (
    <Button
    disabled={VariantActive == null || loading}
    size={size}
    className={clsx(
      "!px-3 !py-2 relative",
      (VariantActive == null || loading) && "!cursor-not-allowed opacity-50 hover:bg-main ",
      className
    )}
    onClick={callback}
  >
    {/* 添加 loading 效果 */}
    {loading ? (
      <div className="flex items-center justify-center gap-2 ">
        <svg 
          className="animate-spin h-4 w-4 text-white" 
          xmlns="http://www.w3.org/2000/svg" 
          fill="none" 
          viewBox="0 0 24 24"
        >
          <circle 
            className="opacity-25" 
            cx="12" 
            cy="12" 
            r="10" 
            stroke="currentColor" 
            strokeWidth="4"
          />
          <path 
            className="opacity-75" 
            fill="currentColor" 
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          />
        </svg>
        <span>{t('nav.loading')}</span>
      </div>
    ) : (
      t(text)
    )}
  </Button>
	);
}
