import { OrderslistDocument } from "@/gql/graphql";
import { executeGraphQL } from "@/lib/utils/graphql";

export const getOrderList = async ({
  page = 1,
  pageSize = 10,

}: {
  page?: number;
  pageSize?: number;

}) => {
  try {
    // 计算游标
    const cursor = page > 1 ? btoa(`arrayconnection:${(page - 1) * pageSize - 1}`) : null;

    const data = await executeGraphQL(OrderslistDocument, {
      cache: "no-cache",
      variables: {
        first: pageSize,
        after: cursor,
        
      },
      clientWithAuth: true,
    });

    return data;
  } catch (error) {
    console.error('Error fetching orders:', error);
    throw error;
  }
}; 