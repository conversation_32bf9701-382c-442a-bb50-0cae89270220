"use client";
import React, { useRef, useState, useEffect } from "react";
import Lightbox from "react-image-lightbox";
import "react-image-lightbox/style.css";
import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay, Controller, Navigation, Pagination } from "swiper/modules";
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";
import { Link } from "@/navigation";
import { containerVariants, itemVariants } from "@/lib/utils/util";
import { motion } from "framer-motion";
import { useTranslations } from "next-intl";
import SEOOptimizedImage from "@/components/Image/SEOOptimizedImage";
import { CountUpNumber } from "@/components/CountUpNumber/page";

// 优化的动画变体
const fadeInUp = {
  hidden: {
    opacity: 0,
    y: 40,
    scale: 0.95
  },
  visible: {
    opacity: 1,
    y: 0,
    scale: 1,
    transition: {
      duration: 0.6,
      ease: [0.25, 0.46, 0.45, 0.94]
    }
  }
};

const fadeInLeft = {
  hidden: {
    opacity: 0,
    x: -40,
    scale: 0.95
  },
  visible: {
    opacity: 1,
    x: 0,
    scale: 1,
    transition: {
      duration: 0.6,
      ease: [0.25, 0.46, 0.45, 0.94]
    }
  }
};

const fadeInRight = {
  hidden: {
    opacity: 0,
    x: 40,
    scale: 0.95
  },
  visible: {
    opacity: 1,
    x: 0,
    scale: 1,
    transition: {
      duration: 0.6,
      ease: [0.25, 0.46, 0.45, 0.94]
    }
  }
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.15,
      delayChildren: 0.1
    }
  }
};

const scaleIn = {
  hidden: {
    opacity: 0,
    scale: 0.8
  },
  visible: {
    opacity: 1,
    scale: 1,
    transition: {
      duration: 0.5,
      ease: [0.25, 0.46, 0.45, 0.94]
    }
  }
};



// SVG 图标组件 - 荣誉资质相关图标 (优化版)
const CertificateIcon = () => (
  <div className="relative p-3 bg-gradient-to-br from-[#0077ed] to-[#0056b3] rounded-xl shadow-lg">
    <svg className="w-8 h-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M12 6v6l4 2" fill="none" />
      <circle cx="12" cy="12" r="3" fill="currentColor" opacity="0.3" />
    </svg>
  </div>
);

const QualityIcon = () => (
  <div className="relative p-3 bg-gradient-to-br from-[#0077ed] to-[#0056b3] rounded-xl shadow-lg">
    <svg className="w-8 h-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12.75L11.25 15 15 9.75m-3-7.036A11.959 11.959 0 013.598 6 11.99 11.99 0 003 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285z" />
      <circle cx="12" cy="10" r="2" fill="currentColor" opacity="0.4" />
    </svg>
  </div>
);

const ISO9001Icon = () => (
  <div className="relative p-3 bg-gradient-to-br from-[#0077ed] to-[#0056b3] rounded-xl shadow-lg">
    <svg className="w-8 h-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <circle cx="12" cy="12" r="9" strokeWidth={1.5} />
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4" />
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M8 12h8M12 8v8" opacity="0.3" />
    </svg>
  </div>
);

const EnvironmentalIcon = () => (
  <div className="relative p-3 bg-gradient-to-br from-[#0077ed] to-[#0056b3] rounded-xl shadow-lg">
    <svg className="w-8 h-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4.26 10.147a60.436 60.436 0 00-.491 6.347A48.627 48.627 0 0112 20.904a48.627 48.627 0 018.232-4.41 60.46 60.46 0 00-.491-6.347m-15.482 0a50.57 50.57 0 00-2.658-.813A59.905 59.905 0 0112 3.493a59.902 59.902 0 0110.399 5.84c-.896.248-1.783.52-2.658.814m-15.482 0A50.697 50.697 0 0112 13.489a50.702 50.702 0 017.74-3.342M6.75 15a.75.75 0 100-1.5.75.75 0 000 1.5zm0 0v-3.675A55.378 55.378 0 0112 8.443m-7.007 11.55A5.981 5.981 0 006.75 15.75v-1.5" />
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M12 8l-2 2 2 2 2-2-2-2z" fill="currentColor" opacity="0.3" />
    </svg>
  </div>
);

const SafetyIcon = () => (
  <div className="relative p-3 bg-gradient-to-br from-[#0077ed] to-[#0056b3] rounded-xl shadow-lg">
    <svg className="w-8 h-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
      <circle cx="12" cy="16" r="1" fill="currentColor" />
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 11h6" opacity="0.4" />
    </svg>
  </div>
);

const InnovationIcon = () => (
  <div className="relative p-3 bg-gradient-to-br from-[#0077ed] to-[#0056b3] rounded-xl shadow-lg">
    <svg className="w-8 h-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
      <circle cx="12" cy="9" r="2" fill="currentColor" opacity="0.4" />
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M10 17h4" />
    </svg>
  </div>
);

const ExcellenceIcon = () => (
  <div className="relative p-3 bg-gradient-to-br from-[#0077ed] to-[#0056b3] rounded-xl shadow-lg">
    <svg className="w-8 h-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
      <circle cx="12" cy="12" r="3" fill="currentColor" opacity="0.3" />
    </svg>
  </div>
);

const TrophyIcon = () => (
  <div className="relative p-3 bg-gradient-to-br from-[#0077ed] to-[#0056b3] rounded-xl shadow-lg">
    <svg className="w-8 h-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M6 8a2 2 0 01-2-2V4a2 2 0 012-2h2m8 0h2a2 2 0 012 2v2a2 2 0 01-2 2h-2m-8 12v-2a2 2 0 012-2h8a2 2 0 012 2v2" />
      <circle cx="12" cy="7" r="2" fill="currentColor" opacity="0.4" />
    </svg>
  </div>
);

const LocationIcon = () => (
  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
    <path d="M12,11.5A2.5,2.5 0 0,1 9.5,9A2.5,2.5 0 0,1 12,6.5A2.5,2.5 0 0,1 14.5,9A2.5,2.5 0 0,1 12,11.5M12,2A7,7 0 0,0 5,9C5,14.25 12,22 12,22C12,22 19,14.25 19,9A7,7 0 0,0 12,2Z" />
  </svg>
);

const PhoneIcon = () => (
  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
    <path d="M6.62,10.79C8.06,13.62 10.38,15.94 13.21,17.38L15.41,15.18C15.69,14.9 16.08,14.82 16.43,14.93C17.55,15.3 18.75,15.5 20,15.5A1,1 0 0,1 21,16.5V20A1,1 0 0,1 20,21A17,17 0 0,1 3,4A1,1 0 0,1 4,3H7.5A1,1 0 0,1 8.5,4C8.5,5.25 8.7,6.45 9.07,7.57C9.18,7.92 9.1,8.31 8.82,8.59L6.62,10.79Z" />
  </svg>
);

const EmailIcon = () => (
  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
    <path d="M20,8L12,13L4,8V6L12,11L20,6M20,4H4C2.89,4 2,4.89 2,6V18A2,2 0 0,0 4,20H20A2,2 0 0,0 22,18V6C22,4.89 21.1,4 20,4Z" />
  </svg>
);

const WebIcon = () => (
  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
    <path d="M16.36,14C16.44,13.34 16.5,12.68 16.5,12C16.5,11.32 16.44,10.66 16.36,10H19.74C19.9,10.64 20,11.31 20,12C20,12.69 19.9,13.36 19.74,14M14.59,19.56C15.19,18.45 15.65,17.25 15.97,16H18.92C17.96,17.65 16.43,18.93 14.59,19.56M14.34,14H9.66C9.56,13.34 9.5,12.68 9.5,12C9.5,11.32 9.56,10.65 9.66,10H14.34C14.43,10.65 14.5,11.32 14.5,12C14.5,12.68 14.43,13.34 14.34,14M12,19.96C11.17,18.76 10.5,17.43 10.09,16H13.91C13.5,17.43 12.83,18.76 12,19.96M8,8H5.08C6.03,6.34 7.57,5.06 9.4,4.44C8.8,5.55 8.35,6.75 8,8M5.08,16H8C8.35,17.25 8.8,18.45 9.4,19.56C7.57,18.93 6.03,17.65 5.08,16M4.26,14C4.1,13.36 4,12.69 4,12C4,11.31 4.1,10.64 4.26,10H7.64C7.56,10.66 7.5,11.32 7.5,12C7.5,12.68 7.56,13.34 7.64,14M12,4.03C12.83,5.23 13.5,6.57 13.91,8H10.09C10.5,6.57 11.17,5.23 12,4.03M18.92,8H15.97C15.65,6.75 15.19,5.55 14.59,4.44C16.43,5.07 17.96,6.34 18.92,8M12,2C6.47,2 2,6.5 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z" />
  </svg>
);

const SearchIcon = () => (
  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
    <path d="M9.5,3A6.5,6.5 0 0,1 16,9.5C16,11.11 15.41,12.59 14.44,13.73L14.71,14H15.5L20.5,19L19,20.5L14,15.5V14.71L13.73,14.44C12.59,15.41 11.11,16 9.5,16A6.5,6.5 0 0,1 3,9.5A6.5,6.5 0 0,1 9.5,3M9.5,5C7,5 5,7 5,9.5C5,12 7,14 9.5,14C12,14 14,12 14,9.5C14,7 12,5 9.5,5Z" />
  </svg>
);

const CheckIcon = () => (
  <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 24 24">
    <path d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z" />
  </svg>
);

function Index() {
  const t = useTranslations("about");
  const [isLightboxOpen, setIsLightboxOpen] = useState(false);
  const [photoIndex, setPhotoIndex] = useState(0);

  // 占位图片数组
  const galleryImages = [
    "/image/about/设备1.jpg",
    "/image/about/设备2.jpg",
    "/image/about/设备3.jpg",
    "/image/about/设备4.jpg",
    "/image/about/设备5.jpg",
    "/image/about/设备6.jpg"
  ];

  // 发展历程数据
  const milestones = [
    {
      year: t("milestone1Year"),
      title: t("milestone1Title"),
      description: t("milestone1Description")
    },
    {
      year: t("milestone2Year"),
      title: t("milestone2Title"),
      description: t("milestone2Description")
    },
    {
      year: t("milestone3Year"),
      title: t("milestone3Title"),
      description: t("milestone3Description")
    },
    {
      year: t("milestone4Year"),
      title: t("milestone4Title"),
      description: t("milestone4Description")
    },
    {
      year: t("milestone5Year"),
      title: t("milestone5Title"),
      description: t("milestone5Description")
    }
  ];

  // 荣誉资质数据
  const honors = [
    { title: t("honor1Title"), icon: <CertificateIcon /> },
    { title: t("honor2Title"), icon: <QualityIcon /> },
    { title: t("honor3Title"), icon: <ISO9001Icon /> },
    { title: t("honor4Title"), icon: <EnvironmentalIcon /> },
    { title: t("honor5Title"), icon: <SafetyIcon /> },
    { title: t("honor6Title"), icon: <InnovationIcon /> },
    { title: t("honor7Title"), icon: <ExcellenceIcon /> },
    { title: t("honor8Title"), icon: <TrophyIcon /> }
  ];

  // 合作伙伴数据
  const partners = [
    { name: "Tsinghua University", logo: "/placeholder-partner-1.jpg" },
    { name: "Peking University", logo: "/placeholder-partner-2.jpg" },
    { name: "Shanghai Jiao Tong University", logo: "/placeholder-partner-3.jpg" },
    { name: "SGS Group", logo: "/placeholder-partner-4.jpg" },
    { name: "KI Group", logo: "/placeholder-partner-5.jpg" },
    { name: "ZTE Corporation", logo: "/placeholder-partner-6.jpg" }
  ];

  return (
    <div className="min-h-screen bg-white">
      {/* 英雄区域 - 协调的灰色主题 */}
      <motion.section
        className="relative min-h-screen flex items-center bg-gradient-to-br from-gray-50 via-gray-100 to-gray-200 overflow-hidden"
        initial="hidden"
        animate="visible"
        variants={staggerContainer}
      >
        {/* 简约背景装饰 */}
        <div className="absolute inset-0">
          <div className="absolute inset-0 bg-gradient-to-br from-white/30 to-gray-100/50"></div>
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-[#0077ed]/3 rounded-full blur-3xl"></div>
          <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-gray-300/20 rounded-full blur-3xl"></div>
        </div>

        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="grid lg:grid-cols-2 gap-12 lg:gap-16 items-center">
            <motion.div className="text-gray-900 space-y-6 lg:space-y-8" variants={fadeInLeft}>
              <div className="space-y-4 lg:space-y-6">
                <motion.div
                  className="inline-block px-4 py-2 bg-white/80 backdrop-blur-sm rounded-full border border-gray-200 shadow-sm"
                  variants={scaleIn}
                >
                  <span className="text-gray-600 text-sm font-medium tracking-wide uppercase">
                    {t("heroSubtitle")}
                  </span>
                </motion.div>

                <h1 className="text-3xl sm:text-4xl lg:text-5xl xl:text-6xl font-bold leading-tight">
                  <span className="block text-gray-900">
                    {t("heroTitle")}
                  </span>
                </h1>

                <p className="text-base sm:text-lg lg:text-xl text-gray-700 leading-relaxed max-w-2xl">
                  {t("heroDescription")}
                </p>
              </div>

              {/* 统计数据 - 使用 CountUpNumber */}
              <motion.div
                className="grid grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6"
                variants={staggerContainer}
              >
                <motion.div className="text-center p-4 lg:p-6 bg-white/80 backdrop-blur-sm rounded-xl border border-gray-200 shadow-sm" variants={scaleIn}>
                  <div className="text-2xl lg:text-3xl xl:text-4xl font-bold text-gray-900 mb-2">
                    <CountUpNumber end="20" className="text-2xl lg:text-3xl xl:text-4xl font-bold text-gray-900" />
                    <span className="text-[#0077ed]">+</span>
                  </div>
                  <div className="text-xs lg:text-sm text-gray-600">{t("yearsExperience")}</div>
                </motion.div>

                <motion.div className="text-center p-4 lg:p-6 bg-white/80 backdrop-blur-sm rounded-xl border border-gray-200 shadow-sm" variants={scaleIn}>
                  <div className="text-2xl lg:text-3xl xl:text-4xl font-bold text-gray-900 mb-2">
                    <CountUpNumber end="88" className="text-2xl lg:text-3xl xl:text-4xl font-bold text-gray-900" />
                  </div>
                  <div className="text-xs lg:text-sm text-gray-600">{t("factoryArea")}</div>
                </motion.div>

                <motion.div className="text-center p-4 lg:p-6 bg-white/80 backdrop-blur-sm rounded-xl border border-gray-200 shadow-sm" variants={scaleIn}>
                  <div className="text-2xl lg:text-3xl xl:text-4xl font-bold text-gray-900 mb-2">
                    <CountUpNumber end="1" className="text-2xl lg:text-3xl xl:text-4xl font-bold text-gray-900" />
                    <span className="text-[#0077ed]">.08</span>
                  </div>
                  <div className="text-xs lg:text-sm text-gray-600">{t("registeredCapital")}</div>
                </motion.div>

                <motion.div className="text-center p-4 lg:p-6 bg-white/80 backdrop-blur-sm rounded-xl border border-gray-200 shadow-sm" variants={scaleIn}>
                  <div className="text-2xl lg:text-3xl xl:text-4xl font-bold text-gray-900 mb-2">
                    <CountUpNumber end="500" className="text-2xl lg:text-3xl xl:text-4xl font-bold text-gray-900" />
                    <span className="text-[#0077ed]">+</span>
                  </div>
                  <div className="text-xs lg:text-sm text-gray-600">{t("employees")}</div>
                </motion.div>
              </motion.div>
            </motion.div>

            {/* 视频/图片区域 - 协调设计 */}
            <motion.div className="relative" variants={fadeInRight}>
              <div className="relative w-full h-64 sm:h-80 lg:h-96 rounded-2xl overflow-hidden shadow-lg">
                <div className="absolute inset-0 bg-gradient-to-br from-gray-200 to-gray-300"></div>
                <div className="relative z-10 w-full h-full flex items-center justify-center">
                  <div className="text-center text-gray-700">
                    <motion.div
                      className="mb-4 lg:mb-6 cursor-pointer"
                      whileHover={{ scale: 1.05 }}
                      transition={{ duration: 0.2 }}
                    >
                      <div className="w-16 lg:w-20 h-16 lg:h-20 mx-auto bg-[#0077ed]/10 backdrop-blur-sm rounded-full flex items-center justify-center border border-[#0077ed]/20">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" className="text-[#0077ed] lg:w-8 lg:h-8">
                          <path d="M8 5V19L19 12L8 5Z" fill="currentColor" />
                        </svg>
                      </div>
                    </motion.div>
                    <h3 className="text-lg lg:text-xl xl:text-2xl font-semibold mb-2 text-gray-900">{t("videoTitle")}</h3>
                    <p className="text-sm lg:text-base text-gray-600">{t("videoDescription")}</p>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </motion.section>

      {/* 公司介绍 - 协调的灰白主题 */}
      <motion.section
        className="py-12 sm:py-16 lg:py-20 bg-gray-50"
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, amount: 0.2 }}
        variants={staggerContainer}
      >
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div className="text-center mb-12 sm:mb-16 lg:mb-20" variants={fadeInUp}>
            <div className="inline-block px-4 py-2 bg-gray-100 rounded-full mb-4">
              <span className="text-gray-600 text-sm font-medium tracking-wide uppercase">
                {t("aboutGropeSubtitle")}
              </span>
            </div>
            <h2 className="text-2xl sm:text-3xl lg:text-4xl xl:text-5xl font-bold text-gray-900 mb-4 lg:mb-6">
              {t("aboutGropeTitle")}
            </h2>
            <p className="text-base sm:text-lg lg:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              {t("po")}
            </p>
          </motion.div>

          <div className="grid lg:grid-cols-2 gap-8 lg:gap-12 xl:gap-16 items-center">
            <motion.div className="space-y-6 lg:space-y-8" variants={fadeInLeft}>
              <motion.div
                className="group bg-white p-6 sm:p-8 lg:p-10 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-200"
                whileHover={{ y: -1 }}
              >
                <div className="flex items-center mb-4 lg:mb-6">
                  {/* <div className="w-10 h-10 lg:w-12 lg:h-12 bg-[#0077ed] rounded-lg flex items-center justify-center mr-3 lg:mr-4">
                    <svg className="w-5 h-5 lg:w-6 lg:h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12 2L2 7L12 12L22 7L12 2Z" />
                      <path d="M2 17L12 22L22 17" />
                      <path d="M2 12L12 17L22 12" />
                    </svg>
                  </div> */}
                  <h3 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 group-hover:text-[#0077ed] transition-colors">
                    {t("companyOverviewTitle")}
                  </h3>
                </div>
                <p className="text-base sm:text-lg text-gray-700 leading-relaxed">
                  {t("companyOverviewContent")}
                </p>
              </motion.div>

              <motion.div
                className="group bg-white p-6 sm:p-8 lg:p-10 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-200"
                whileHover={{ y: -1 }}
              >
                <div className="flex items-center mb-4 lg:mb-6">
                  {/* <div className="w-10 h-10 lg:w-12 lg:h-12 bg-gray-700 rounded-lg flex items-center justify-center mr-3 lg:mr-4">
                    <svg className="w-5 h-5 lg:w-6 lg:h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12 2C13.1 2 14 2.9 14 4V8C14 9.1 13.1 10 12 10S10 9.1 10 8V4C10 2.9 10.9 2 12 2M21 9V7L15 7.95V9C15 11.1 13.1 13 11 13V14.5C11 15.3 11.7 16 12.5 16S14 15.3 14 14.5V14H16V14.5C16 16.4 14.4 18 12.5 18S9 16.4 9 14.5V13C6.9 13 5 11.1 5 9V7.95L3 7V9C3 11.8 5.2 14 8 14V16H6V18H18V16H16V14C18.8 14 21 11.8 21 9Z" />
                    </svg>
                  </div> */}
                  <h3 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 group-hover:text-gray-700 transition-colors">
                    {t("companyMissionTitle")}
                  </h3>
                </div>
                <p className="text-base sm:text-lg text-gray-700 leading-relaxed">
                  {t("companyMissionContent")}
                </p>
              </motion.div>
            </motion.div>

            <motion.div className="relative" variants={fadeInRight}>
              <div className="relative rounded-xl overflow-hidden shadow-lg">
                <SEOOptimizedImage
                  src="/image/about/49cceab7281352c939923ffc24cae01.jpg"
                  alt={t("factoryImageTitle")}
                  width={800}
                  height={800}
                  quality={100}
                  className="w-full h-full object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>
                <div className="absolute bottom-0 left-0 right-0 p-6 sm:p-8 lg:p-10 text-white">
                  <h4 className="text-xl sm:text-2xl lg:text-3xl font-bold mb-2 lg:mb-3">{t("factoryImageTitle")}</h4>
                  <p className="text-sm sm:text-base lg:text-lg xl:text-xl text-white/90 leading-relaxed">{t("factoryImageDescription")}</p>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </motion.section>

      {/* 发展历程 */}
      <motion.section
        className="py-20 bg-white"
      >
        <div className="container mx-auto px-4">
          <motion.div className="text-center mb-16" variants={fadeInUp}>
            <h2 className="text-4xl lg:text-5xl font-bold text-themeSecondary900 mb-4">{t("timelineTitle")}</h2>
            <p className="text-xl text-themePrimary600 uppercase tracking-wider">{t("timelineSubtitle")}</p>
          </motion.div>

          <div className="relative max-w-4xl mx-auto">
            {/* 时间线 */}
            <div className="absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-gradient-to-b from-themePrimary500 to-themePrimary700 rounded-full hidden lg:block"></div>

            <div className="space-y-12">
              {milestones.map((milestone, index) => (
                <motion.div
                  key={index}
                  className={`flex items-center ${index % 2 === 0 ? 'lg:flex-row' : 'lg:flex-row-reverse'} flex-col lg:gap-8`}
                  variants={fadeInUp}
                  transition={{ delay: index * 0.2 }}
                >
                  <div className={`w-full lg:w-5/12 ${index % 2 === 0 ? 'lg:text-right' : 'lg:text-left'} text-center lg:mb-0 mb-4`}>
                    <div className="bg-white p-8 rounded-2xl shadow-lg border border-themeSecondary200 hover:shadow-xl transition-shadow duration-300">
                      <div className="text-3xl font-bold text-themePrimary600 mb-3">{milestone.year}</div>
                      <h3 className="text-xl font-semibold text-themeSecondary900 mb-3">{milestone.title}</h3>
                      <p className="text-themeSecondary700 leading-relaxed">{milestone.description}</p>
                    </div>
                  </div>

                  {/* 时间点 */}
                  <div className="relative z-10 w-6 h-6 bg-themePrimary600 rounded-full border-4 border-white shadow-lg hidden lg:block">
                    <div className="absolute inset-0 bg-themePrimary600 rounded-full animate-ping opacity-75"></div>
                  </div>

                  <div className="w-full lg:w-5/12"></div>
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </motion.section>

      {/* 核心产品 */}
      {/* <motion.section
        className="py-20 bg-[#f5f5f7]"
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, amount: 0.3 }}
        variants={staggerContainer}
      >
        <div className="container mx-auto px-4">
          <motion.div className="text-center mb-16" variants={fadeInUp}>
            <h2 className="text-4xl lg:text-5xl font-bold text-black mb-4">{t("coreProductsTitle")}</h2>
            <p className="text-xl text-[#0077ed] uppercase tracking-wider">{t("coreProductsSubtitle")}</p>
          </motion.div>

          <motion.div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8" variants={staggerContainer}>
            <motion.div className="bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300 hover:-translate-y-2" variants={imageScale}>
              <div className="h-48 overflow-hidden">
                <motion.img
                  src="/placeholder-product-1.jpg"
                  alt={t("product1Title")}
                  className="w-full h-full object-cover hover:scale-110 transition-transform duration-300"
                  variants={imageScale}
                />
              </div>
              <div className="p-6">
                <h3 className="text-xl font-semibold text-black mb-3">{t("product1Title")}</h3>
                <p className="text-black leading-relaxed">{t("product1Description")}</p>
              </div>
            </motion.div>

            <motion.div className="bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300 hover:-translate-y-2" variants={imageScale}>
              <div className="h-48 overflow-hidden">
                <motion.img
                  src="/placeholder-product-2.jpg"
                  alt={t("product2Title")}
                  className="w-full h-full object-cover hover:scale-110 transition-transform duration-300"
                  variants={imageScale}
                />
              </div>
              <div className="p-6">
                <h3 className="text-xl font-semibold text-black mb-3">{t("product2Title")}</h3>
                <p className="text-black leading-relaxed">{t("product2Description")}</p>
              </div>
            </motion.div>

            <motion.div className="bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300 hover:-translate-y-2" variants={imageScale}>
              <div className="h-48 overflow-hidden">
                <motion.img
                  src="/placeholder-product-3.jpg"
                  alt={t("product3Title")}
                  className="w-full h-full object-cover hover:scale-110 transition-transform duration-300"
                  variants={imageScale}
                />
              </div>
              <div className="p-6">
                <h3 className="text-xl font-semibold text-black mb-3">{t("product3Title")}</h3>
                <p className="text-black leading-relaxed">{t("product3Description")}</p>
              </div>
            </motion.div>

            <motion.div className="bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300 hover:-translate-y-2" variants={imageScale}>
              <div className="h-48 overflow-hidden">
                <motion.img
                  src="/placeholder-product-4.jpg"
                  alt={t("product4Title")}
                  className="w-full h-full object-cover hover:scale-110 transition-transform duration-300"
                  variants={imageScale}
                />
              </div>
              <div className="p-6">
                <h3 className="text-xl font-semibold text-black mb-3">{t("product4Title")}</h3>
                <p className="text-black leading-relaxed">{t("product4Description")}</p>
              </div>
            </motion.div>

            <motion.div className="bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300 hover:-translate-y-2" variants={imageScale}>
              <div className="h-48 overflow-hidden">
                <motion.img
                  src="/placeholder-product-5.jpg"
                  alt={t("product5Title")}
                  className="w-full h-full object-cover hover:scale-110 transition-transform duration-300"
                  variants={imageScale}
                />
              </div>
              <div className="p-6">
                <h3 className="text-xl font-semibold text-black mb-3">{t("product5Title")}</h3>
                <p className="text-black leading-relaxed">{t("product5Description")}</p>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </motion.section> */}

      {/* 荣誉资质 - 协调的灰色主题 */}
      <motion.section
        className="py-12 sm:py-16 lg:py-20 bg-gray-100"
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, amount: 0.2 }}
        variants={staggerContainer}
      >
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div className="text-center mb-12 sm:mb-16 lg:mb-20" variants={fadeInUp}>
            <div className="inline-block px-4 py-2 bg-gray-200 rounded-full mb-4 shadow-sm border border-gray-300">
              <span className="text-gray-600 text-sm font-medium tracking-wide uppercase">
                {t("honorsSubtitle")}
              </span>
            </div>
            <h2 className="text-2xl sm:text-3xl lg:text-4xl xl:text-5xl font-bold text-gray-900 mb-4 lg:mb-6">
              {t("honorsTitle")}
            </h2>
            <p className="text-base sm:text-lg lg:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              {t("honorsDescription")}
            </p>
          </motion.div>

          <motion.div
            className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 lg:gap-8"
            variants={staggerContainer}
          >
            {honors.map((honor, index) => (
              <motion.div
                key={index}
                className="group bg-white p-6 sm:p-8 lg:p-10 rounded-xl text-center shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 cursor-pointer"
                variants={scaleIn}
                whileHover={{
                  y: -2,
                  transition: { duration: 0.2 }
                }}
              >
                {/* 图标 */}
                <div className="mb-4 lg:mb-6 flex justify-center">
                  <motion.div
                    className="transform group-hover:scale-105 transition-all duration-200"
                  >
                    {honor.icon}
                  </motion.div>
                </div>

                {/* 标题 */}
                <h3 className="text-base sm:text-lg lg:text-xl font-bold text-gray-900 leading-tight group-hover:text-[#0077ed] transition-colors duration-200">
                  {honor.title}
                </h3>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </motion.section>

      {/* 技术创新 - 深灰主题 */}
      <motion.section
        className="py-12 sm:py-16 lg:py-20 bg-gray-200 text-gray-900 relative overflow-hidden"
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, amount: 0.2 }}
        variants={staggerContainer}
      >
        {/* 简约背景装饰 */}
        <div className="absolute inset-0">
          <div className="absolute top-20 left-20 w-64 h-64 bg-[#0077ed]/5 rounded-full blur-3xl"></div>
          <div className="absolute bottom-20 right-20 w-80 h-80 bg-gray-400/20 rounded-full blur-3xl"></div>
        </div>

        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <motion.div className="text-center mb-12 sm:mb-16 lg:mb-20" variants={fadeInUp}>
            <div className="inline-block px-4 py-2 bg-white/80 rounded-full mb-4 border border-gray-300 shadow-sm">
              <span className="text-gray-600 text-sm font-medium tracking-wide uppercase">
                {t("innovationSubtitle")}
              </span>
            </div>
            <h2 className="text-2xl sm:text-3xl lg:text-4xl xl:text-5xl font-bold mb-4 lg:mb-6">
              {t("innovationTitle")}
            </h2>
            <p className="text-base sm:text-lg lg:text-xl text-gray-700 max-w-3xl mx-auto leading-relaxed">
              {t("innovationDescription")}
            </p>
          </motion.div>

          <div className="grid lg:grid-cols-2 gap-8 lg:gap-12 xl:gap-16 items-center">
            <motion.div className="space-y-6 lg:space-y-8" variants={fadeInLeft}>
              {/* 专利统计 - 使用 CountUpNumber */}
              <motion.div className="grid grid-cols-2 gap-3 sm:gap-4 lg:gap-6" variants={staggerContainer}>
                <motion.div
                  className="bg-white/80 backdrop-blur-lg p-4 sm:p-6 lg:p-8 rounded-lg text-center border border-gray-300 hover:bg-white transition-all duration-200 shadow-sm"
                  variants={scaleIn}
                  whileHover={{ scale: 1.02 }}
                >
                  <div className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-2">
                    <CountUpNumber end="1" className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900" />
                  </div>
                  <div className="text-xs sm:text-sm lg:text-base text-gray-600">{t("inventionPatents")}</div>
                </motion.div>

                <motion.div
                  className="bg-white/80 backdrop-blur-lg p-4 sm:p-6 lg:p-8 rounded-lg text-center border border-gray-300 hover:bg-white transition-all duration-200 shadow-sm"
                  variants={scaleIn}
                  whileHover={{ scale: 1.02 }}
                >
                  <div className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-2">
                    <CountUpNumber end="6" className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900" />
                  </div>
                  <div className="text-xs sm:text-sm lg:text-base text-gray-600">{t("utilityPatents")}</div>
                </motion.div>

                <motion.div
                  className="bg-white/80 backdrop-blur-lg p-4 sm:p-6 lg:p-8 rounded-lg text-center border border-gray-300 hover:bg-white transition-all duration-200 shadow-sm"
                  variants={scaleIn}
                  whileHover={{ scale: 1.02 }}
                >
                  <div className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-2">
                    <CountUpNumber end="18" className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900" />
                  </div>
                  <div className="text-xs sm:text-sm lg:text-base text-gray-600">{t("designPatents")}</div>
                </motion.div>

                <motion.div
                  className="bg-white/80 backdrop-blur-lg p-4 sm:p-6 lg:p-8 rounded-lg text-center border border-gray-300 hover:bg-white transition-all duration-200 shadow-sm"
                  variants={scaleIn}
                  whileHover={{ scale: 1.02 }}
                >
                  <div className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-2">
                    <CountUpNumber end="6" className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900" />
                  </div>
                  <div className="text-xs sm:text-sm lg:text-base text-gray-600">{t("softwareCopyrights")}</div>
                </motion.div>
              </motion.div>

              {/* 技术亮点 */}
              <motion.div
                className="bg-white/80 backdrop-blur-lg p-6 sm:p-8 lg:p-10 rounded-xl border border-gray-300 shadow-sm"
                variants={fadeInUp}
              >
                <h3 className="text-xl sm:text-2xl lg:text-3xl font-bold mb-6 lg:mb-8 text-center lg:text-left text-gray-900">{t("techHighlightsTitle")}</h3>
                <ul className="space-y-4 lg:space-y-6">
                  {[1, 2, 3, 4].map((num) => (
                    <motion.li
                      key={num}
                      className="flex items-start text-base sm:text-lg lg:text-xl"
                      variants={fadeInLeft}
                      whileHover={{ x: 3 }}
                      transition={{ duration: 0.2 }}
                    >
                      <div className="flex-shrink-0 w-5 h-5 lg:w-6 lg:h-6 bg-[#0077ed] rounded-full flex items-center justify-center mr-3 lg:mr-4 mt-1">
                        <CheckIcon />
                      </div>
                      <span className="text-gray-700">{t(`techHighlight${num}`)}</span>
                    </motion.li>
                  ))}
                </ul>
              </motion.div>
            </motion.div>

            <motion.div className="relative" variants={fadeInRight}>
              <div className="relative rounded-xl overflow-hidden shadow-lg">
                <SEOOptimizedImage
                  src="/image/about/469e3b4780c75a25cbd76822a951414.jpg"
                  alt={t("smartFactoryTitle")}
                  width={800}
                  height={800}
                  quality={100}
                  className="w-full h-full object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent"></div>
                <div className="absolute bottom-0 left-0 right-0 p-6 sm:p-8 lg:p-10 text-white">
                  <h4 className="text-xl sm:text-2xl lg:text-3xl font-bold mb-2 lg:mb-3">{t("smartFactoryTitle")}</h4>
                  <p className="text-sm sm:text-base lg:text-lg xl:text-xl text-white/90 leading-relaxed">{t("smartFactoryDescription")}</p>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </motion.section>

      {/* 合作伙伴 */}
      {/* <motion.section
        className="py-20 bg-[#f5f5f7]"
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, amount: 0.3 }}
        variants={staggerContainer}
      >
        <div className="container mx-auto px-4">
          <motion.div className="text-center mb-16" variants={fadeInUp}>
            <h2 className="text-4xl lg:text-5xl font-bold text-black mb-4">{t("partnersTitle")}</h2>
            <p className="text-xl text-[#0077ed] uppercase tracking-wider">{t("partnersSubtitle")}</p>
          </motion.div>

          <motion.div className="mb-12" variants={fadeInUp}>
            <Swiper
              modules={[Autoplay, Navigation]}
              spaceBetween={30}
              slidesPerView={6}
              autoplay={{
                delay: 3000,
                disableOnInteraction: false,
              }}
              navigation
              loop={true}
              breakpoints={{
                320: { slidesPerView: 2 },
                768: { slidesPerView: 4 },
                1024: { slidesPerView: 6 },
              }}
              className="py-8"
            >
              {partners.map((partner, index) => (
                <SwiperSlide key={index}>
                  <div className="w-32 h-20 bg-white rounded-xl flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                    <img
                      src={partner.logo}
                      alt={partner.name}
                      className="max-w-20 max-h-12 object-contain grayscale hover:grayscale-0 transition-all duration-300"
                    />
                  </div>
                </SwiperSlide>
              ))}
            </Swiper>
          </motion.div>

          <motion.div className="text-center max-w-4xl mx-auto" variants={fadeInUp}>
            <p className="text-lg text-black leading-relaxed">
              {t("partnersDescription")}
            </p>
          </motion.div>
        </div>
      </motion.section> */}

      {/* 图片画廊 - 协调的灰白主题 */}
      <motion.section
        className="py-12 sm:py-16 lg:py-20 bg-gray-50"
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, amount: 0.2 }}
        variants={staggerContainer}
      >
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div className="text-center mb-12 sm:mb-16 lg:mb-20" variants={fadeInUp}>
            <div className="inline-block px-4 py-2 bg-white rounded-full mb-4 shadow-sm border border-gray-200">
              <span className="text-gray-600 text-sm font-medium tracking-wide uppercase">
                {t("gallerySubtitle")}
              </span>
            </div>
            <h2 className="text-2xl sm:text-3xl lg:text-4xl xl:text-5xl font-bold text-gray-900 mb-4 lg:mb-6">
              {t("galleryTitle")}
            </h2>
            <p className="text-base sm:text-lg lg:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              {t("wik")}
            </p>
          </motion.div>

          <motion.div
            className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8"
            variants={staggerContainer}
          >
            {galleryImages.map((image, index) => (
              <motion.div
                key={index}
                className="group relative h-48 sm:h-64 lg:h-80 rounded-xl overflow-hidden cursor-pointer shadow-md hover:shadow-lg transition-all duration-300"
                variants={scaleIn}
                whileHover={{
                  y: -1,
                  transition: { duration: 0.2 }
                }}
                onClick={() => {
                  setPhotoIndex(index);
                  setIsLightboxOpen(true);
                }}
              >
                <SEOOptimizedImage
                  src={image}
                  alt={`${t("galleryImageAlt")} ${index + 1}`}
                  width={800}
                  height={800}
                  quality={100}
                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                />

                {/* 悬停遮罩 */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-black/10 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-300"></div>

                {/* 搜索图标 */}
                <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300">
                  <div className="w-10 h-10 lg:w-12 lg:h-12 bg-[#0077ed] text-white rounded-full flex items-center justify-center">
                    <SearchIcon />
                  </div>
                </div>

                {/* 图片编号 */}
                <div className="absolute top-3 left-3 lg:top-4 lg:left-4 w-6 h-6 lg:w-8 lg:h-8 bg-black/50 backdrop-blur-sm rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300">
                  <span className="text-white text-xs lg:text-sm font-medium">{index + 1}</span>
                </div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </motion.section>

      {/* 联系我们 */}
      {/* <motion.section
        className="py-20 bg-gradient-to-br from-themeSecondary800 via-themePrimary700 to-themePrimary600 text-white"
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, amount: 0.3 }}
        variants={staggerContainer}
      >
        <div className="container">
          <motion.div className="text-center mb-16" variants={fadeInUp}>
            <h2 className="text-4xl lg:text-5xl font-bold mb-4">{t("contactTitle")}</h2>
            <p className="text-xl text-white/70 uppercase tracking-wider">{t("contactSubtitle")}</p>
          </motion.div>
          <motion.div className="bg-white/10 backdrop-blur-lg p-8 rounded-3xl" variants={fadeInRight}>
            <h3 className="text-3xl font-bold mb-4 text-white">{t("cooperationTitle")}</h3>
            <p className="text-lg leading-relaxed mb-8 text-white/80">
              {t("cooperationDescription")}
            </p>
            <Link href="/contact-us" className="bg-white text-themePrimary600 px-8 py-4 rounded-full text-lg font-semibold hover:bg-themeSecondary50 hover:text-themePrimary700 transition-all duration-300 hover:-translate-y-1 hover:shadow-lg">
              {t("consultButton")}
            </Link>
          </motion.div>
        </div>
      </motion.section> */}

      {/* Lightbox */}
      {isLightboxOpen && (
        <Lightbox
          mainSrc={galleryImages[photoIndex]}
          nextSrc={galleryImages[(photoIndex + 1) % galleryImages.length]}
          prevSrc={galleryImages[(photoIndex + galleryImages.length - 1) % galleryImages.length]}
          onCloseRequest={() => setIsLightboxOpen(false)}
          onMovePrevRequest={() =>
            setPhotoIndex((photoIndex + galleryImages.length - 1) % galleryImages.length)
          }
          onMoveNextRequest={() =>
            setPhotoIndex((photoIndex + 1) % galleryImages.length)
          }
        />
      )}
    </div>
  );
}

export default React.memo(Index);

