
fragment OrderItem on Order {
  id
  checkoutId
  status
  billingAddress {
    city
    cityArea
    companyName
    countryArea
    country {
      code
    }
    id
    lastName
    phone
    streetAddress1
    streetAddress2
  }
  shippingAddress {
    city
    cityArea
    companyName
    countryArea
    country {
      code
    }
    firstName
    id
    lastName
    phone
    streetAddress1
    streetAddress2
  }
  lines {
    id
    quantity
    totalPrice {
      gross {
        amount
        currency
      }
      tax {
        amount
        currency
      }
      net {
        amount
        currency
      }
    }
    variant {
      ...ProductVariantItem
      product {
        media: metafield(key: "media")
        name
        slug
      }
    }
  }
    total {
currency
    }
    totalCharged{
      amount
currency

    }
}