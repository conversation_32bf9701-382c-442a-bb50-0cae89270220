"use client";
import { getTranslations } from "next-intl/server";
import GetInstantQuoteButton from "../Contact/GetInstantQuote";
import GetLatestPrice from "./GetLatestPrice";

 const ProductInfo =  ({ product }) => {
	

	const attributes = product.attributes.map((item) => {
		return {
			label: item.attribute?.translation?.name || item.attribute.name,
			value: item.values.map((i) => i.translation?.name || i.name).join(", "),
		};
	});

	return (
		<>
			{/* <div className="border-b border-[#cad8df]">
				<h2 className=" text-xl font-bold leading-[2.19rem] text-[#212121]">
					{product?.translation?.name || product?.name}
				</h2>
			</div> */}

			<div>
				{/* <h2 className="mt-6 text-2xl font-bold leading-8 text-[#000000]">{t("base.product-details")}</h2> */}

				<div className="mt-6 space-y-2">
					{attributes?.map((item, i) => {
						return (
							<div key={i} className="flex ">
								<span className="block max-w-10 font-bold">{item.label}:</span>
								<h2 className="flex-1 font-bold">{item.value}</h2>
							</div>
						);
					})}
				</div>
				{/* <GetInstantQuoteButton text={<>{t("base.get-quote")}</>} className="mt-11 w-fit !justify-start" /> */}
			</div>
		</>
	);
};

export default ProductInfo