'use client'
import React from 'react';
import SwiperCore from 'swiper';
import  { Pagination } from  'swiper/modules'
import { Swiper, SwiperSlide } from 'swiper/react';
import 'swiper/swiper-bundle.css';
import { useTranslations } from "next-intl";
import { Empty } from "antd";
import { type Blog } from "@/lib/@types/api/blog";

import BlogItemDefault from "@/components/Blogs/BlogItemDefault";

// 初始化 Swiper 模块
SwiperCore.use([Pagination]);

const LatestNews = ({blogList,title}:{blogList:Blog.BlogListItem[],title?:string}) => {
	const t=useTranslations()


	
	return (
		<div className="container py-10 ">
				<h2 className="text-4xl font-bold text-center ">{title ?title :t('home.latestnews')}</h2>
			{!title &&<p className="text-sm text-gray-500 text-center my-2 ">
				{t("blog.dec")}
			</p>}
			{blogList&&blogList.length>0 ?<Swiper
				spaceBetween={10}
				slidesPerView={1}
				pagination={{ clickable: true }}
				breakpoints={{
					640: {
						slidesPerView: 1,
					},
					768: {
						slidesPerView: 2,
					},
					1024: {
						slidesPerView: 3,
					},
					1600: {
						slidesPerView: 4,
					},
				}}
				className="animate__animated animate__fadeIn !p-4 !pb-14"
			>
				{blogList.map((news) => (
					<SwiperSlide key={news.blog_id}>
						<BlogItemDefault news={news}></BlogItemDefault>
					</SwiperSlide>
				))}
			</Swiper>:<div><Empty /></div>}
		</div>
	);
};

export default LatestNews;
