fragment CheckoutItem on Checkout {
	id
	email
      discount {
      amount
      currency
    }
        giftCards {
displayCode
      id
      last4CodeChars
    }
      billingAddress {
      city
      cityArea
      companyName
      countryArea
        country {
          code
        }
      id
      lastName
      phone
      streetAddress1
      streetAddress2
    }
      shippingAddress {
      city
      cityArea
      companyName
      countryArea
        country {
          code
        }
      firstName
      id
      lastName
      phone
      streetAddress1
      streetAddress2
    }
	shippingMethod {
      id
      name
      message
    }
        availableShippingMethods{
            id
         }
    shippingMethods{
            id
         }
     availableCollectionPoints{
            id
         }
	lines {
		id
		quantity
		totalPrice {
			gross {
				amount
				currency
			}
			tax {
				amount
				currency
			}
			net {
				amount
				currency
			}
		}
		variant {
			...ProductVariantItem
              product {
         media: metafield(key: "media")
          name
          slug
          chargeTaxes
        }
		}

	}
	totalPrice {
		gross {
			amount
			currency
		}
		net {
			amount
			currency
		}
		tax {
			amount
			currency
		}
	}
}
