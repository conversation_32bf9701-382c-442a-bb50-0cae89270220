'use client'
import { But<PERSON> } from "antd";
import { ArrowRightOutlined } from "@ant-design/icons";
import React from "react";
import { useTranslations } from "next-intl";
import { Link } from "@/navigation";

const   EmailUsBtn=({className}:{className?:string})=>{
	const t=useTranslations()

	return (
		<Link  href={`mailto:<EMAIL>`} target="_blank" className={`flex justify-center ${className}`}>
			<Button  size="large" type="default" shape="round"  ghost className="group border !border-main hover:!bg-main hover:!text-white">
				{t("base.emailUs")}<ArrowRightOutlined className="transition-all transform duration-700 group-hover:translate-x-2 "/></Button>
		</Link>
	)
}

export  default  EmailUsBtn
