import Image, { ImageProps as NextImageProps } from "next/image";

interface CustomImageProps extends Omit<NextImageProps, "src"> {
	src: string;
	overrideSrc?: string;
}

const CustomImage = ({
	src,
	alt,
	width,
	height,
	className,
	style,
	priority = true,
	quality = 75,
	fill = false,
	sizes = "100vw",
	onClick,
	overrideSrc,
	...rest
}: CustomImageProps) => {
	const finalSrc = src;

	return (
		<div>
			<Image
				src={finalSrc}
				overrideSrc={overrideSrc || finalSrc}
				alt={alt}
				width={width}
				height={height}
				className={className}
				style={style}
				priority={priority}
				quality={quality}
				fill={fill}
				sizes={sizes}
				title={alt}
				onClick={onClick}
				{...rest}
			/>
			<noscript>
				<img src={finalSrc} alt={alt} width={width} height={height} title={alt} />
			</noscript>
		</div>
	);
};

export default CustomImage;
