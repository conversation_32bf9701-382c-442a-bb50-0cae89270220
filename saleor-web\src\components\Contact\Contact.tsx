"use client"
import React from 'react';
import { MailOutlined, SkypeOutlined, WhatsAppOutlined, EnvironmentOutlined } from '@ant-design/icons';
import ContactForm from "@/components/InquryForm/ContactForm";
import { useTranslations } from "next-intl";
import CusTitle from "@/components/Home2/CusTitle";
import { contactInfo } from "@/lib/contacts";


const Contact = ({locale}:{ locale: string }) => {

const t=useTranslations()

	return (
		<div className="bg-white py-12  ">
			<div className=" container grid grid-cols-2 max-md:grid-cols-1 shadow-mainShadow rounded-xl">
				<div className="flex flex-col justify-around m-5    max-md:border-none  border-r-[1.5px] pr-5 border-r-gray-300 ">
					<div>
						<CusTitle homeTitle={t('menu.contactUs')} className="text-left !py-0"></CusTitle>
						<p className=" text-gray-500 text-left mt-2" >{t('contactUS.content')}</p>
					</div>

					<div>
						<h3 className="text-2xl font-bold mb-6">{t('contactUS.ContactInformation')}</h3>
						<ul className="space-y-4">
							<li className="flex items-center text-lg ">
								<MailOutlined className=" text-xl mr-3  bg-main !text-white rounded-full p-2" />
								<span className="font-bold">{contactInfo.email}</span>
							</li>
							<li className="flex items-center text-lg">
								<WhatsAppOutlined className=" text-xl mr-3  bg-main !text-white rounded-full p-2" />
								<span className="font-bold">	{contactInfo.whatsapp}</span>
							</li>
							<li className="flex items-center text-lg">
								<EnvironmentOutlined className=" text-xl mr-3  bg-main !text-white rounded-full p-2" />
								<span className="font-bold">{contactInfo.address}</span>
							</li>
						</ul>
					</div>
					<div>
						<h3 className="text-2xl font-bold mt-8 mb-6">{t('contactUS.BusinessHours')}</h3>
						<ul className="space-y-2 text-lg">
							<li><span className="text-blue-600">●</span> <strong>{t('contactUS.mTof')}:</strong> 9:00 AM - 6:00 PM</li>
							<li><span className="text-blue-600">●</span> <strong>{t('contactUS.Saturday')}:</strong> 10:00 AM - 4:00 PM</li>
							<li><span className="text-blue-600">●</span> <strong>{t('contactUS.Sunday')}:</strong> {t('contactUS.Closed')}</li>
						</ul>
					</div>
				</div>
				<ContactForm locale={locale} title={t('base.getInstantQuoteAndTS')}
        // @ts-ignore
										 titleContent={t("form.fillOutIn")} className="!py-0 " innerClassName="!shadow-none !rounded-none"></ContactForm>

			</div>

		</div>
	);
};

export default Contact;
