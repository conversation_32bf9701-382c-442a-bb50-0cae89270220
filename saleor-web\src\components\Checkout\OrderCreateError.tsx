"use client";
import { useTranslations } from "next-intl";
import { Link } from "@/navigation";
import React from "react";

export default function OrderCreateError() {
	const t = useTranslations("order");


	return (
		<div className="container flex h-[80vh] flex-col items-center justify-center ">
			<div className="flex flex-col items-center justify-center p-8 text-center">
				<div className="mb-4 text-5xl text-red-500">
					<svg
						xmlns="http://www.w3.org/2000/svg"
						className="h-16 w-16"
						fill="none"
						viewBox="0 0 24 24"
						stroke="currentColor"
					>
						<path
							strokeLinecap="round"
							strokeLinejoin="round"
							strokeWidth={2}
							d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
						/>
					</svg>
				</div>
				<h2 className="mb-2 text-2xl font-bold text-gray-800">{t("Order Creation Failed")}</h2>
				<p className="mb-4 text-gray-600">{t("Sorry")}</p>

				<Link href={`/`}>
					<button className="rounded-md bg-blue-500 px-6 py-2 text-white transition-colors hover:bg-blue-600">
						{t("Try Again")}
					</button>
				</Link>
			</div>
		</div>
	);
}
