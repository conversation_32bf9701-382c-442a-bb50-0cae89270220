# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

/.vscode
# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts
/test-results/
/playwright-report/
/playwright/.cache/

.idea/
.user.ini
.git/
