import { BaseMyResponse } from "@/lib/@types/api/base";
import { DataType } from "@/lib/@types/base";
import { UserLoginDocument } from "@/gql/graphql";

export declare namespace UserApi {
	interface UserEmail {
		user: {
			email: string;
      id: string;
		};
	}
	interface UserInfo extends UserEmail {
		token: string;
	}

	interface RegisterUserInfo extends UserEmail {}
	interface PasswordChangeResp extends UserEmail {}
}
