"use client";
import React from "react";
import { Collapse } from "antd";
import { useTranslations } from "next-intl";
export interface faqData {
	key: string|number;
	label: string;
	children: React.ReactNode;
};

const FAQ = ({itemsData}:{itemsData?:faqData[]}) => {
	const t=useTranslations('common')
  // FAQ items 数组
  const items = [
    {
      key: '0',
      label: t('What'),                    // "What is 3D Printing Service?"
      children: <div className="p-4">{t('3D printing')}</div>  // "3D printing service is..."
    },
    {
      key: '1',
      label: t('How long'),                // "How long does order processing take?"
      children: <div className="p-4">{t('Order processing')}</div>  // "Order processing time..."
    },
    {
      key: '2',
      label: t('What formats'),            // "What file formats are supported?"
      children: <div className="p-4">{t('We support')}</div>  // "We support various..."
    },
    {
      key: '3',
      label: t('How ensure'),              // "How do you ensure print quality?"
      children: (
        <div className="p-4">
          {t('We ensure')}
          <ul className="list-disc pl-4 mt-2">
            <li>{t('High precision')}</li>
            <li>{t('Strict quality')}</li>
            <li>{t('Professional team')}</li>
          </ul>
        </div>
      )
    },
    {
      key: '4',
      label: t('Do provide'),              // "Do you provide shipping services?"
      children: <div className="p-4">{t('Yes provide')}</div>  // "Yes, we provide..."
    }
  ];
	return (
		<div className="py-12">
			<div className="rounded-lg bg-white p-4 ">
				<Collapse
					size="middle"
					accordion
					expandIconPosition="end"
					className="site-collapse-custom-collapse"
					bordered={false}
					defaultActiveKey={["0"]}
					items={itemsData||items} // 直接使用 items
				/>
			</div>
		</div>
	);
};

export default FAQ;
