import React from "react";
import {  ConfigProvider } from "antd"; // 导入Ant Design应用和配置提供者
import {ModalInquiryProvider} from '@/context/ModalInquiryContext'
import { theme } from "@/lib/theme";
import ModalCustomer from "@/components/Modal/ModalCustomer";
import { ModalCustomerProvider } from "@/context/CustomerContext"; // 导入主题配置

const GlobalProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
	return (
		<ConfigProvider theme={theme}>
			{/* 提供主题配置 */}
			<ModalInquiryProvider>
<ModalCustomerProvider>
	{children}

</ModalCustomerProvider>
			</ModalInquiryProvider>
		</ConfigProvider>
	);
};

export default GlobalProvider;
