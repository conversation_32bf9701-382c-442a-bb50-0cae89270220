"use client";

import React, { useEffect, useRef, useState } from "react";
import styled from "styled-components";
import { useLocale, useTranslations } from "next-intl";
import { ClockCircleFilled, ProfileOutlined } from "@ant-design/icons";
import BlogSearch from "@/components/Blogs/BlogSeach.tsx";
import { Blog } from "@/lib/@types/api/blog";
import { Link } from "@/navigation.ts";
import { Avatar } from "antd";
import moment from "moment";
import FollowUs from "../Social/FollowUs";
import Categories from "./Categories";
import FeatureBlogs from "./FeaturedBlogs";
import { SocialShare } from "@/components/Other/Share";

// 使用 TailwindCSS 的一些自定义样式来优化文章显示
export const StyledArticle = styled.article`
	/* 定义与全局无关的样式 */
	p {
		display: block;
		margin-block-start: 1em;
		margin-block-end: 1em;
		margin-inline-start: 0px;
		margin-inline-end: 0px;
		unicode-bidi: isolate;
	}

	h1 {
		color: #18181b;
		font-weight: 700;
		font-size: 28px;
		padding: 4px 0;
	}

	/* 列表样式 */
	ul {
		list-style-type: disc;
		margin-left: 20px;
		padding-left: 0;
	}

	li {
		margin-bottom: 10px;
	}

	strong {
		padding: 0 2px;
	}

	/* 标题样式 */
	h2 {
		color: #18181b;
		font-weight: 700;
		font-size: 24px;
		padding: 4px 0;
	}

	h3 {
		color: #18181b;
		font-weight: 700;
		font-size: 20px;
		padding: 4px 0;
	}

	h4 {
		color: #18181b;
		font-weight: 600;
		font-size: 16px;
		padding: 4px 0;
	}

	h5 {
		color: #18181b;
		font-weight: 600;
		font-size: 14px;
		padding: 4px 0;
	}

	h6 {
		color: #18181b;
		font-weight: 500;
		font-size: 13px;
		padding: 4px 0;
	}

	span {
		color: #18181b;
		font-weight: 400;
		font-size: 16px;
		padding: 4px 0;
	}

	/* 表格样式 */
	table {
		width: 100%;
		border-collapse: collapse;
		margin-top: 1em;
		margin-bottom: 1em;
	}

	th,
	td {
		border: 1px solid #dcdcdc;
		padding: 8px;
		text-align: left;
	}

	/* 表格内文字 */
	table p {
		margin: 0;
	}
	a {
		padding: 0 2px;
	}
    img {
  display: inline-block;
  white-space: nowrap;
}

	/* 添加其他必要的样式，确保不被全局样式覆盖 */
`;


const BlogArticle = ({
	content,
	blogList,
	blog,
	clsList
}: {
	content: string;
	blogList: Blog.BlogListItem[];
	blog?: any;
	clsList?: any;
}) => {
	const [headings, setHeadings] = useState<string[]>([]);
	const [newContent, setNewContent] = useState<string>(content);

	// 提取文章的 H1-H5 标题并生成目录，同时更新 newContent
	useEffect(() => {
		const parser = new DOMParser();
		const doc = parser.parseFromString(content, "text/html");

		// 提取标题并生成 ID
		const foundHeadings = Array.from(doc.querySelectorAll("h1, h2, h3, h4, h5")).map((h, index) => {
			const headingText = h.textContent || "";
			const headingId = headingText.trim().toLowerCase().replace(/\s+/g, "-");
			h.id = headingId;
			return headingText;
		});

		// 更新内容为带有 ID 的 HTML
		const updatedContent = new XMLSerializer().serializeToString(doc);
		setNewContent(updatedContent);
		setHeadings(foundHeadings);
	}, [content]);

	const t = useTranslations();
	let locale = useLocale()
	return (
		<>
			<div className="relative flex justify-between gap-y-8 gap-x-12 max-lg:flex-wrap">
				<div className="flex-1 min-w-0">
					{/* 文章头部 */}
					<header className="mb-8 pb-8 border-b border-gray-200">
						<div className="text-center max-w-4xl mx-auto">
							<h1 className="text-3xl md:text-4xl font-bold text-gray-900 leading-tight mb-6">
								{blog?.blog_title}
							</h1>

							{/* 分类标签 */}
							<div className="flex justify-center items-center gap-2 mb-4">
								{blog?.blog_classification_list.map((cls) => (
									<span
										className="inline-block px-3 py-1 text-xs font-medium text-white bg-black rounded-full hover:bg-gray-800 transition-colors duration-300"
										key={cls.cls_id}
									>
										{cls.cls_name}
									</span>
								))}
							</div>

							{/* 文章信息 */}
							<div className="text-sm text-gray-600 flex items-center justify-center gap-4 flex-wrap">
								<span className="flex items-center gap-1">
									<ClockCircleFilled className="text-gray-400" />
									{moment(blog?.upload_time).format("MMM DD, YYYY")}
								</span>
								<span>•</span>
								<span>{t("blog.PostedBy")} {process.env.NEXT_PUBLIC_COMPANY_NAME}</span>
							</div>
						</div>
					</header>

					{/* 文章内容 */}
					<StyledArticle className="prose prose-lg max-w-none">
						<div
							className="blog-content"
							dangerouslySetInnerHTML={{
								__html: newContent,
							}}
						/>
					</StyledArticle>
				</div>

				{/* 右侧边栏 */}
				<aside className="w-full lg:w-80 xl:w-96 space-y-10">
					<Categories clsList={clsList} />

					{/* 推荐文章 */}
					<div className="bg-gradient-to-br from-gray-50 to-white rounded-xl p-6 shadow-sm border border-gray-100">
						<FeatureBlogs locale={locale} />
					</div>

					{/* 标签和分享 */}
					<div className="space-y-6">
						{/* 标签 */}
						{blog?.blog_tag_list && blog.blog_tag_list.length > 0 && (
							<div className="bg-gradient-to-br from-gray-50 to-white rounded-xl p-6 shadow-sm border border-gray-100">
								<h3 className="text-lg font-bold text-gray-900 mb-4 flex items-center gap-2">
									<span className="w-2 h-2 bg-black rounded-full"></span>
									{t("blog.tags")}
								</h3>
								<div className="flex flex-wrap gap-2">
									{blog.blog_tag_list.map((tag) => (
										<Link
											href={`/tag/${tag.tag_slug}`}
											key={tag.tag_id}
											className="inline-flex items-center px-3 py-1.5 text-sm font-medium text-gray-700 bg-white border border-gray-200 rounded-full hover:bg-gray-50 hover:border-gray-300 hover:shadow-sm transition-all duration-300 cursor-pointer hover:text-main"
										>
											<span className="text-gray-400 mr-1">#</span>
											{tag.tag_name}
										</Link>
									))}
								</div>
							</div>
						)}

						{/* 分享 */}
						<div className="bg-gradient-to-br from-gray-50 to-white rounded-xl p-6 shadow-sm border border-gray-100">
							<h3 className="text-lg font-bold text-gray-900 mb-4 flex items-center gap-2">
								<span className="w-2 h-2 bg-black rounded-full"></span>
								{t("blog.Share")}
							</h3>
							<SocialShare />
						</div>
					</div>
				</aside>
			</div>
		</>
	);
};

const RelatedBlog = ({ blogList }: { blogList: Blog.BlogListItem[] }) => {
	const t = useTranslations();

	return (
		<div className="bg-white rounded-lg border border-gray-200 p-6">
			<h3 className="text-lg font-semibold text-gray-900 mb-4 pb-2 border-b border-gray-200">
				{t("blog.RelatedBlogs")}
			</h3>
			<ul className="space-y-4">
				{blogList &&
					blogList.map((blog) => (
						<li key={blog.blog_id} className="border-b border-gray-100 last:border-b-0 pb-4 last:pb-0">
							<Link href={"/blog/" + blog.blog_slug} className="block group">
								<h4 className="text-base font-medium text-gray-900 group-hover:text-black transition-colors duration-300 line-clamp-2 mb-2">
									{blog.blog_title}
								</h4>
								{blog.blog_classification_list &&
									blog.blog_classification_list.map((item, index) => (
										index === 0 && (
											<span className="text-xs font-medium text-gray-500 uppercase tracking-wide" key={item.cls_slug}>
												{item.cls_name}
											</span>
										)
									))}
							</Link>
						</li>
					))}
			</ul>
		</div>
	);
};

export default BlogArticle;
