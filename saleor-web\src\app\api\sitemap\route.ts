import { getBlogCategoriesLinks, getBlogLinks, getIsEditMenus, getProductCategoriesLinks, getProductLinks } from '@/lib/utils/sitemap-util';
import { cApiUrl, createLanguageSitemap, createSitemap } from '@/lib/utils/util';
import { withXMLResponse } from '@/lib/utils/withXMLResponse';
import { NextApiRequest, NextApiResponse } from 'next'
import { NextRequest, NextResponse } from 'next/server'
import { locales } from "@/config";

export const GET= async (req: Request)=> {
  if (req.method !== "GET") {
    return new NextResponse(null, {
      status: 405
    });
  }
  const isLang = process.env.NEXT_PUBLIC_IS_I18N == "true";

    // 未开启多语言
    if (!isLang) {
      const promise = [
        getIsEditMenus(),
        getProductLinks(),
        getBlogLinks(),
        getProductCategoriesLinks(),
        getBlogCategoriesLinks()
      ];
      const result = await Promise.all(promise);

      const allMap = result.flat().map(m => m);
      return new NextResponse(createSitemap(allMap), {
        headers: {
          'Content-Type': 'application/xml',
        },
      })
    }

  
    const urls: string[] = locales.map(lang => {
      return `${cApiUrl}/api/sitemap/${lang}`;
    });


    return new NextResponse(createLanguageSitemap(urls), {
      headers: {
        'Content-Type': 'application/xml',
      },
    })

}