import { Link } from "@/navigation";
import Image from "next/image";
import { Tooltip } from "antd";

const ProductImageBox = ({ product }) => {
	const url = JSON.parse(product?.node.media)[0]?.url;
	return (
		<Link href={`/product/${product.node.slug}`} className="">
			<div className=" rounded-xl border p-4 duration-300 hover:shadow-card">
				<div className="overflow-hidden">
					<Image
						className="aspect-square w-full  object-cover transition-transform duration-500 hover:scale-105"
						objectFit="cover"
						quality={100}
						width={220}
						height={220}
						src={url||'/image/default-image.webp'}
						alt="Product Image"
					></Image>
				</div>
				<Tooltip title={product.node?.name}>
					<p className="mt-5 line-clamp-1 text-black hover:text-main">
						{product.node?.translation?.name || product.node?.name}
					</p>
				</Tooltip>
			</div>
		</Link>
	);
};

export default ProductImageBox;
