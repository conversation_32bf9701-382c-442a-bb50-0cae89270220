import { User<PERSON><PERSON> } from "@/lib/@types/api/user";
import { useLocale, useTranslations } from "next-intl";
import { useUserStore } from "@/lib/store/user";
import { useUserStore as useUserStore2 } from "@/store/user.store";
import { useRouter } from "@/navigation";
import { BaseMyResponse } from "@/lib/@types/api/base";
import { message } from "antd";
import { setCookie } from "cookies-next";

export const useUserAuth = () => {
	const locale = useLocale();
	const { setUserInfo, userInfo } = useUserStore();
  const { userInfo:userInfo2 } = useUserStore2();
	const router = useRouter();
  const t = useTranslations();
	const isLogin = () => {
		return !!userInfo2?.token;
	};
	const useLogin = async ({ email, password }: { email: string; password: string }) => {
		const resp = (await fetch("/api/auth/login", {
			method: "POST",
			body: JSON.stringify({
				email,
				password,
			}),
			headers: {
				"Accept-Language": locale,
				"Content-Type": "application/json",
			},
		}).then((r) => r.json())) as BaseMyResponse<UserApi.UserInfo>;

		if (resp.code === 200) {

      
      setCookie("__user__login__info", {
        username:resp.data.user.email,
				...resp.data,
        ...resp.data.user,
			});


			setUserInfo({
				user: resp.data.user,
				token: resp.data.token,
			});
 
      message.success(t("message.login successfully"));
			return resp;
		} else {
      message.error(t('message.Please'));
		}
	};

	// 修改密码
	const usePasswordChange = async ({
		oldPassword,
		newPassword,
	}: {
		oldPassword: string;
		newPassword: string;
	}) => {
		const resp = (await fetch("/api/auth/password-change", {
			method: "POST",
			body: JSON.stringify({
				oldPassword,
				newPassword,
			}),
			headers: {
				"Accept-Language": locale,
				Authorization: "Bearer " + userInfo?.token,
			},
		}).then((r) => r.json())) as BaseMyResponse<UserApi.PasswordChangeResp>;
		if (resp.code !== 200) {
			throw new Error(resp.msg);
		} else {
			useSignOut();
		}
		return resp;
	};

	const useSignOut = (to?: boolean) => {
		setUserInfo(null);
		if (to) router.replace("/");
	};
	const useSignUp = async ({
		channel,
		email,
		password,
	}: {
		channel: string;
		email: string;
		password: string;
	}) => {
		return (await fetch("/api/auth/register", {
			method: "POST",
			headers: {
				"Content-Type": "application/json",
				"Accept-Language": locale,
			},
			body: JSON.stringify({
				password: password,
				email: email,
				channel: channel,
			}),
		}).then((r) => r.json())) as BaseMyResponse<UserApi.RegisterUserInfo>;
	};
	return {
		useLogin,
		usePasswordChange,
		useSignOut,
		useSignUp,
		isLogin,
	};
};
