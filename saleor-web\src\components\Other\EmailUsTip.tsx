"use client";
import React from "react";
import { useTranslations } from "next-intl";

export default function EmailUsTip({ tip }: { tip: string }) {
	const t = useTranslations();
	return (
		<section className="flex w-full items-center justify-between py-10 text-sm max-md:flex-wrap">
			<img src="/image/tips.png" alt="material Selection bottom" className="h-10 w-10 object-fill"></img>
			<p className="font-bold">{tip}</p>
			<a
				href="mailto:<EMAIL>"
				className="float-right cursor-pointer rounded-full bg-main px-4 py-3 text-white"
			>
				{t("base.emailBtn")}
			</a>
		</section>
	);
}
