import { type Metadata } from "next";
import { type OpenGraphType } from "next/dist/lib/metadata/types/opengraph-types";
import { locales } from "@/config";

type Props = {
	params: {
		page: string[];
		locale: string;
	};
};

type BaseSeo = {
	title: string;
	description: string;
	keywords: string[];
};

type Options = BaseSeo & {
	ogImg?: string;
	ogType?: OpenGraphType;
	currentPath?: string;
	author?: string;
};

export const generateSeo = (props: Props, options: Options, metadata?: Metadata): Metadata => {
	const urlPath = options?.currentPath ? options.currentPath : "/" + (props.params?.page?.join("/") || "");
	const locale = props.params.locale;
	const icon = "/favicon.ico";
	const languages: Record<string, string> = {};
	locales.map((code) => {
		languages[code] = `/${code}${urlPath}`;
	});
	const { title, description, keywords, ogImg, ogType } = options;

	return {
		generator: "Next.js", // 告诉浏览器生成该网页的主要框架技术
		referrer: "origin-when-cross-origin", // 控制浏览器发送 HTTP Referer 头的方式。这里的值 "origin-when-cross-origin" 意味着如果请求是跨域的，只发送原始文档的源作为引用者。
		formatDetection: {
			email: false,
			address: false,
			telephone: false,
		}, // 通知设备或浏览器不要自动识别和格式化页面上可能出现的电话号码、地址或电子邮件地址。这有助于防止浏览器将这些文本转换为可点击的链接，可能是出于设计或用户体验的考虑。
		title,
		keywords:keywords&& keywords.join(","),
		description,
		alternates: {
			canonical: urlPath,
			languages,
		},
		metadataBase: new URL(process.env.NEXT_PUBLIC_SITE_URL||""), // 设置基础URL，用于生成其他URL。
		appleWebApp: {
			capable: true,
			statusBarStyle: "black-translucent",
			title,
		},
		openGraph: {
			images: ogImg || icon,
			title,
			type: ogType || "website",
			url: urlPath,
			description,
			locale,
			siteName: process.env.NEXT_PUBLIC_COMPANY_NAME,
		},
		twitter: {
			title,
			description,
			site: "@site",
			creator: "@creator",
			images: ogImg || icon,
			card: "summary_large_image",
		},
		icons: {
			apple: icon,
			icon,
			shortcut: icon,
			other: {
				rel: "apple-touch-icon-precomposed",
				url: icon,
			},
		},
		publisher: process.env.NEXT_PUBLIC_COMPANY_NAME,
		robots: {
			index: true,
			follow: true,
			nocache: false,
			googleBot: {
				index: true,
				follow: true,
				noimageindex: false,
				"max-video-preview": -1,
				"max-image-preview": "large",
				"max-snippet": -1,
			},
		},
		authors: [
			{
				name: options?.author || process.env.NEXT_PUBLIC_COMPANY_NAME,
			},
		],
		verification: {
			google: "google", // 填写谷歌网站验证
			// other 可填写其他meta标签
		},
		...metadata,
	};
};
