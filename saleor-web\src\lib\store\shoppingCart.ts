import { persist } from "zustand/middleware";
import { DataType } from "@/lib/@types/base";
import { CheckoutFindDocument } from "@/gql/graphql";
import { create } from "zustand";

export type ShoppingCartListType = DataType<typeof CheckoutFindDocument>["checkout"] | null;
type State = {
	checkoutIds: Record<string, string> | null;
	setCheckoutIds: (checkoutIds: Record<string, string> | null) => void;
	addCheckoutId: (channel: string, checkoutId: string) => void;
	getCheckoutId: (channel: string) => string;
	removeCheckoutId: (channel: string) => void;
	cartList: ShoppingCartListType;
	setCartList: (cartList: ShoppingCartListType) => void;
	cartCount: number;
	clearAllData: () => void;

  // 立即购买函数
  isCart: boolean ;//是否立即购买
  setisCart: (isCart: boolean) => void;
  BuyBowcheckoutId: string | null;
  BuyBowCartList: ShoppingCartListType;
  BuyBowsetCartList:(cartList: ShoppingCartListType) => void;
};
export const useShoppingCartStore = create(
	persist<State>(
		(set, get) => ({
			checkoutIds: null,
			setCheckoutIds: (checkoutIds) => set({ checkoutIds }),
			addCheckoutId: (channel, checkoutId) => {
				const checkoutIds = (JSON.parse(JSON.stringify(get().checkoutIds)) || {}) as Record<string, string>;
				checkoutIds[channel] = checkoutId;
				get().setCheckoutIds(checkoutIds);
			},
			getCheckoutId: (channel) => {
				const checkoutIds = (JSON.parse(JSON.stringify(get().checkoutIds)) || {}) as Record<string, string>;
				return checkoutIds[channel] || "";
			},
			removeCheckoutId: (channel) => {
				const checkoutIds = (JSON.parse(JSON.stringify(get().checkoutIds)) || {}) as Record<string, string>;
				delete checkoutIds[channel];
				get().setCheckoutIds(checkoutIds);
			},
			cartList: null,
			setCartList: (cartList) => {
				set({ cartList });
				const totalQuantity = cartList?.lines.reduce((accumulator, item) => accumulator + item.quantity, 0);
				set({ cartCount: totalQuantity || 0 });
			},
			cartCount: 0,
			clearAllData: () => {
				set({ checkoutIds: null, cartList: null, cartCount: 0 });
			},

      // 立即购买的函数
      isCart:true,
      setisCart: (isCart) => {
        set({ isCart });
      },
      BuyBowcheckoutId: null,
      BuyBowCartList: null,
      BuyBowsetCartList:(BuyBowCartList) => {
        console.log(BuyBowCartList,'BuyBowCartList2');
        
				set({ BuyBowCartList, BuyBowcheckoutId:BuyBowCartList.id});

			},
		}),
		{
			name: "shopping-cart-store",
		},
	),
);
