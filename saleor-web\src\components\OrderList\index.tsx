"use client";

import { getOrderList } from "@/lib/api/order";
import React, { useEffect, useState } from "react";
import { Table, Card, Tag, Space, Button, message, Pagination, Empty, Modal } from "antd";
import { useTranslations } from "next-intl";
import { useProductStore } from "@/lib/store/product.store";
import { 
  FileTextOutlined,    // 草稿
  ClockCircleOutlined, // 未确认
  InboxOutlined,       // 未发货
  SyncOutlined,        // 部分发货
  RollbackOutlined,    // 部分退货
  UndoOutlined,        // 已退货
  CheckCircleOutlined, // 已发货
  CloseCircleOutlined, // 已取消
  StopOutlined,
  QuestionCircleOutlined,        // 已过期
} from '@ant-design/icons';
import moment from "moment";
import OrderViews from "./OrderViews";
import useIsMobile from "@/lib/hooks/useIsMobile";
import clsx from "clsx";
function Index() {
	const t = useTranslations();
	const [loading, setLoading] = useState(false);
const { currencyUnit } = useProductStore();
	//点单数据
	const [orderData, setOrderData] = useState<any>({});
	const [pagination, setPagination] = useState({
		current: 1,
		pageSize: 10,
		total: 0,
	});



  const [OrderInfo, setOrderInfo] = useState<any>({});

  //是否打开
  const [isModalOpen, setIsModalOpen] = useState(false);

  const showModal = () => {
    setIsModalOpen(true);
  };

  const handleOk = () => {
    setIsModalOpen(false);
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };
// 订单状态映射
const orderStatusMap = {
  DRAFT: { 
    color: '#8c8c8c',
    bgColor: '#f5f5f5',
    text: t('order.Draft'),
    icon: <FileTextOutlined />
  },
  UNCONFIRMED: { 
    color: '#faad14',
    bgColor: '#fff7e6',
    text: t('order.Unconfirmed'),
    icon: <ClockCircleOutlined />
  },
  UNFULFILLED: { 
    color: '#fa8c16',
    bgColor: '#fff2e8',
    text: t('order.Unfulfilled'),
    icon: <InboxOutlined />
  },
  PARTIALLY_FULFILLED: { 
    color: '#1890ff',
    bgColor: '#e6f7ff',
    text: t('order.Partially Fulfilled'),
    icon: <SyncOutlined spin />
  },
  PARTIALLY_RETURNED: { 
    color: '#faad14',
    bgColor: '#fff7e6',
    text: t('order.Partially Returned'),
    icon: <RollbackOutlined />
  },
  RETURNED: { 
    color: '#ff4d4f',
    bgColor: '#fff1f0',
    text: t('order.Returned'),
    icon: <UndoOutlined />
  },
  FULFILLED: { 
    color: '#52c41a',
    bgColor: '#f6ffed',
    text: t('order.Fulfilled'),
    icon: <CheckCircleOutlined />
  },
  CANCELED: { 
    color: '#ff4d4f',
    bgColor: '#fff1f0',
    text: t('order.Canceled'),
    icon: <CloseCircleOutlined />
  },
  EXPIRED: { 
    color: '#8c8c8c',
    bgColor: '#f5f5f5',
    text: t('order.Expired'),
    icon: <StopOutlined />
  }
} as const;

const handleViewDetails = async (orderData: any) => {
  
  await new Promise((resolve) => {
    setOrderInfo(JSON.parse(JSON.stringify(orderData)));
    resolve(true);
  });
  setIsModalOpen(true);
};
	// 表格列定义
	const columns = [
		{
			title: t("order.Created"),
			dataIndex: "created",
			key: "created",
			render: (_, row) => new Date(row.node.created).toLocaleDateString(),
		},
    {
      title: t("order.Status"),
      dataIndex: "status",
      key: "status",
      render: (_, row) => {
        const status = row.node.status;
        const statusConfig = orderStatusMap[status as keyof typeof orderStatusMap] || {
          color: '#d9d9d9',
          bgColor: '#f5f5f5',
          text: status,
          icon: <FileTextOutlined />
        };
    
        return (
          <>
          <span className="max-md:hidden block">
          <Tag
            style={{
              color: statusConfig.color,
              backgroundColor: statusConfig.bgColor,
              border: `1px solid ${statusConfig.color}`,
              borderRadius: '12px',
              padding: '4px 12px',
              display: 'inline-flex',
              alignItems: 'center',
              gap: '4px',
              fontSize: '13px'
            }}
          >
            {React.cloneElement(statusConfig.icon as React.ReactElement, {
              style: { fontSize: '14px' }
            })}
            <span>{statusConfig.text}</span>
          </Tag>
          </span>

          <span className={clsx("hidden max-md:block",`text-[${statusConfig.color}]`)}>{statusConfig.text}</span>

          </>

        );
      },
    },
		{
			title: t("order.Total"),
			dataIndex: "total",
			key: "total",
			render: (_, row) => {
				return (
					<span>
					  <span className="text-main">{currencyUnit} {row.node.totalCharged.amount}</span> {t("order.for")} {row.node.lines.length} {t("order.items")}
					</span>
				);
			},
		},

		{
		  title: t('order.Actions'),
		  key: 'actions',
		  render: (_: any, row: any) => (
		    <Space>
		      <Button type="link" size="small" onClick={() => handleViewDetails(row)}>
		        {t('order.View Details')}
		      </Button>
		    </Space>
		  ),
		},
	];

	useEffect(() => {
		getdata();
	}, [pagination.current, pagination.pageSize]);

	// 获取订单数据
	async function getdata() {
		try {
			setLoading(true);
			const data = await getOrderList({
				page: pagination.current,
				pageSize: pagination.pageSize,
			});

			console.log(data.me.orders);

			setPagination({
				...pagination,
				total: data.me.orders.totalCount,
			});
			setOrderData(data.me.orders);
		} catch (error) {
			message.error(error.message);
		} finally {
			setLoading(false);
		}
	}

	// 处理分页变化
	const handleTableChange = (page: number, pageSize: number) => {
		setPagination({
			...pagination,
			current: page,
			pageSize: pageSize,
		});
	};
  let isMobile=useIsMobile()
  const filteredColumns = isMobile 
  ? columns.filter((col) => col.key !== 'total') // 隐藏 'total' 列
  : columns;
	return (
		<div className="containernone mx-auto py-8 OrderList">
			<Card
				title={t("order.My Orders")}
				extra={
					<Button type="primary" onClick={() => getdata()}>
						{t("order.Refresh")}
					</Button>
				}
			>
				<Table
					columns={filteredColumns}
					dataSource={orderData.edges}
					rowKey={(row) => row.node.id}
					loading={loading}
					pagination={false}
					locale={{
						emptyText: <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description={t("order.No Orders")} />,
					}}
				/>

				{pagination.total > 0 && (
					<div className="my-4 flex justify-end">
						<Pagination
							current={pagination.current}
							pageSize={pagination.pageSize}
							total={pagination.total}
							onChange={handleTableChange}
							showSizeChanger
							showTotal={(total) => t("order.Total Items", { total })}
						/>
					</div>
				)}
			</Card>

			<Modal
				width={800}
				title={t("order.View Details")}
				open={isModalOpen}
				onOk={() => setIsModalOpen(false)}
				onCancel={() => setIsModalOpen(false)}
				destroyOnClose={true}
			>
				{isModalOpen && <OrderViews key={OrderInfo?.node?.id} order={OrderInfo} />}
			</Modal>
		</div>
	);
}

export default Index;
