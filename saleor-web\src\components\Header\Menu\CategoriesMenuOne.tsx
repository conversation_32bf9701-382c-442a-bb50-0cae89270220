import { Link } from "@/navigation.ts";
import Image from "next/image";
import { filterCateImg } from "@/components/Home2/Collection.tsx";
import { defaultLocale } from "@/config.ts";
import React from "react";
import { useCategory } from "@/context/CategoriesContext.tsx";

const CategoriesMenuOne=({locale}:{locale:string})=> {
	const { categories } = useCategory();
	return <div
		className="mega-menu  grid   absolute  left-0 bg-gray-50 w-screen px-3 py-5  rounded-b-sm      max-h-[500px] overflow-y-auto ">
		<ul className="grid grid-cols-3 gap-2 h-fit col-span-2">
			{categories?.edges.map(item => {
				return <li key={item.node.id}
									 className="shadow-sm border bg-white border-gray-200 hover:shadow-main hover:bg-[#004ead29] transition-all duration-700 p-2 rounded-lg relative group ">
					<Link href={"/" + item.node.slug} className="text-black flex  items-center justify-between">
						<div className="flex items-center gap-x-2">
							<Image src={filterCateImg(item.node.metadata)[0]?.url || "/image/default-image.webp"} width={50}
										 quality={50} height={50} alt={item.node.name} className="object-cover rounded  " />
							<div
								className="font-bold">{locale === defaultLocale ? item.node.name : (item.node.translation.name || item.node.name)}</div>
						</div>
						{/*{item.node.children.edges.length>0&&<div className=" float-right text-2xl*/}
						{/*text-blue-500 border border-main shadow rounded-full w-6 h-6 flex items-center justify-center mr-2">{item.node.children.edges.length}</div>*/}
						{/*}*/}
					</Link>
					{
						item.node.children.edges.length > 0 && <div
							className="max-h-0  group-hover:max-h-[500px]  absolute top-[84px] left-0  w-full bg-white  transition-all transform duration-700   rounded-b-sm   overflow-hidden  z-10 shadow-lg shadow-main">
							<ul className="space-y-2  w-full grid grid-cols-1">
								{item.node.children?.edges.map(item => {
									return <li key={item.node.id}
														 className="w-full shadow-sm inline-block  border bg-white border-gray-50 hover:shadow-main hover:bg-[#004ead29] transition-all duration-700 p-2 rounded-lg relative">
										<Link href={'/' + item.node.slug} className="w-full text-black flex    items-center ">
											<Image
												src={filterCateImg(item.node.metadata)[0]?.url || "/image/default-image.webp"}
												width={50} quality={50} height={50} alt={item.node.name}
												className="object-cover rounded " />
											<div className="font-bold">{item.node.name}</div>
										</Link>

									</li>;
								})}
							</ul>
						</div>
					}

				</li>;
			})}
		</ul>
	</div>
}

export default CategoriesMenuOne
