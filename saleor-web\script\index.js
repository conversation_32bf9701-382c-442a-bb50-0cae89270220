import { getLangs } from './writeLanguagePack.js'
import { getSaleorAppToken} from "./writeSaleorAppToken.js";
import { getMLToken } from "./writeMLToken.js";
import { getTranslationList } from "./getTanslationList.js";

/**
 * 异步执行写入操作的函数
 * 该函数负责按顺序调用其他异步函数，以完成一系列写入任务
 */
const write = async () => {
	try {
		// 调用函数以写入MLToken，这是初始化设置的一部分
		await getMLToken();

		// 调用函数以写入Saleor应用令牌，对于应用的认证和授权是必要的
		await getSaleorAppToken();

		// 最后，调用函数以写入语言包，确保应用支持多语言
		await getLangs();

		// 获取翻译后的文件存入文件夹中
		await getTranslationList();
	} catch (error) {
		console.error("An error occurred during the write process:", error);
	}
};

// 执行write函数，启动整个写入流程
write();
