.login-popup {
    opacity: 0;
    visibility: hidden;
    transform: translateY(30px);
    transition: all ease 0.4s;
    z-index: 11;

    &.open {
        opacity: 1;
        visibility: visible;
        transform: translateY(0);
    }

    @media (max-width: 1490px) {
        right: 16px;
    }

    @media (max-width: 767.98px) {
        top: 56px !important;
    }
}

.modal-newsletter,
.modal-video-block,
.modal-sizeguide-block,
.modal-quickview-block,
.modal-cart-block,
.modal-wishlist-block,
.modal-search-block {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    visibility: hidden;
    transition: all ease 0.4s;
    background: rgba($color: #000000, $alpha: 0.6);
    z-index: 101;

    &:has(.modal-newsletter-main.open),
    &:has(.modal-video-main.open),
    &:has(.modal-sizeguide-main.open),
    &:has(.modal-quickview-main.open),
    &:has(.modal-cart-main.open),
    &:has(.modal-wishlist-main.open),
    &:has(.modal-search-main.open) {
        opacity: 1;
        visibility: visible;
    }

    .modal-newsletter-main {
        width: 100%;
        transform: translateY(-100%);
        opacity: 0;
        visibility: hidden;
        transition: all ease 0.6s;

        .right {
            position: relative;

            .list {
                @media (min-width: 640px) {
                    position: absolute;
                    width: calc(100% - 42px);
                    height: -webkit-fill-available;
                }

                @media (max-width: 639.98px) {
                    .product-item {

                        &:last-child,
                        &:first-child {
                            display: none;
                        }
                    }
                }
            }
        }

        &.open {
            transform: translateY(0);
            opacity: 1;
            visibility: visible;
        }
    }

    .modal-quickview-main,
    .modal-cart-main,
    .modal-wishlist-main {
        position: absolute;
        top: 20px;
        right: -100%;
        width: 540px;
        //height: calc(100vh - 40px);
        background-color: var(--white);
        border-radius: 32px;
        overflow: hidden;
        transition: all ease 0.5s;

        &.open {
            right: 20px;
        }

        .remove-cart-btn {
            &:hover {
                svg {
                    color: var(--black);
                }
            }
        }

        .list-product {
            max-height: calc(100% - 140px - 30px + 14px);
            overflow-x: auto;

            .item {
                &:last-child {
                    border-bottom: none;
                }
            }
        }

        .list-product::-webkit-scrollbar {
            width: 6px;
            background: var(--line);
        }

        .list-product::-webkit-scrollbar-thumb {
            background-color: var(--secondary2);
            border-radius: 4px;
        }

        .footer-modal {
            box-shadow: 0px 5px 18px 5px rgba(64, 72, 87, 0.15);
        }

        @media (max-width: 1023.98px) {
            .list-product {
                max-height: calc(100% - 140px - 30px + 24px);
            }
        }

        @media (max-width: 767.98px) {
            width: 480px;
            border-radius: 16px;

            .list-product {
                max-height: calc(100% - 140px - 30px + 42px);
            }
        }

        @media (max-width: 575.98px) {
            width: unset;
            left: 20px;
            top: 30px;
            // height: calc(100vh - 60px);

            .list-product {
                .item {
                    .bg-img {
                        width: 80px;
                        flex-shrink: 0;

                        img {
                            width: 80px;
                        }
                    }
                }
            }
        }
    }

    .modal-quickview-main {
        width: 856px;

        .list-img {
            max-height: calc(100%);
            height: 100%;
            overflow-x: auto;

            .bg-img {
                &:first-child {
                    margin-top: 0;
                }
            }
        }

        .product-infor {
            overflow-x: auto;
            max-height: calc(100% - 54px);
            height: 100%;
        }

        .list-img::-webkit-scrollbar,
        .product-infor::-webkit-scrollbar {
            width: 6px;
            background: var(--line);
        }

        .list-img::-webkit-scrollbar-thumb,
        .product-infor::-webkit-scrollbar-thumb {
            background-color: var(--secondary2);
            border-radius: 4px;
        }

        @media (max-width: 1023.98px) {
            width: 720px;

            .list-product {
                max-height: calc(100% - 210px - 212px);
            }
        }

        @media (max-width: 767.98px) {
            width: 480px;

            >div {
                overflow-x: auto;
                max-height: calc(100%);
                height: 100%;

                &::-webkit-scrollbar {
                    width: 6px;
                    background: var(--line);
                }

                &::-webkit-scrollbar-thumb {
                    background-color: var(--secondary2);
                    border-radius: 4px;
                }
            }

            .list-img::-webkit-scrollbar {
                width: 2px;
            }

            .product-infor {
                overflow-x: unset;
                max-height: unset;
                height: 100%;
            }
        }

        @media (max-width: 575.98px) {
            width: unset;

            .list-product {
                max-height: calc(100% - 210px - 165px);
            }
        }
    }

    .modal-cart-main {
        width: 980px;

        .list-product {
            max-height: calc(100% - 210px - 180px);
        }

        .tab-item {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            background-color: var(--white);
            opacity: 0;
            visibility: hidden;
            transition: all ease 0.4s;
            transform: translateY(100%);
            box-shadow: 0px 5px 18px 5px rgba(64, 72, 87, 0.15);

            &.active {
                opacity: 1;
                visibility: visible;
                transform: translateY(0);
            }
        }

        @media (max-width: 1023.98px) {
            width: 720px;

            .list-product {
                max-height: calc(100% - 210px - 212px);
            }
        }

        @media (max-width: 767.98px) {
            width: 480px;

            .list-product {
                max-height: calc(100% - 210px - 125px);
            }
        }

        @media (max-width: 575.98px) {
            width: unset;

            .list-product {
                max-height: calc(100% - 210px - 165px);
            }
        }
    }

    .modal-cart-main {
        width: 980px;

        .list-product {
            max-height: calc(100% - 210px - 180px);
        }

        .tab-item {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            background-color: var(--white);
            opacity: 0;
            visibility: hidden;
            transition: all ease 0.4s;
            transform: translateY(100%);
            box-shadow: 0px 5px 18px 5px rgba(64, 72, 87, 0.15);

            &.active {
                opacity: 1;
                visibility: visible;
                transform: translateY(0);
            }
        }

        @media (max-width: 1023.98px) {
            width: 720px;

            .list-product {
                max-height: calc(100% - 210px - 212px);
            }
        }

        @media (max-width: 767.98px) {
            width: 480px;

            .list-product {
                max-height: calc(100% - 210px - 125px);
            }
        }

        @media (max-width: 575.98px) {
            width: unset;

            .list-product {
                max-height: calc(100% - 210px - 165px);
            }
        }
    }

    .modal-search-main {
        position: absolute;
        width: calc((1290px / 6) * 5);
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background-color: var(--white);
        transition: all ease 0.5s;
        opacity: 0;
        visibility: hidden;
        overflow: auto;

        &.open {
            opacity: 1;
            visibility: visible;
        }

        .list-product {
            .product-item {
                .list-action {
                    grid-template-columns: repeat(1, minmax(0, 1fr));
                    transform: translateY(120px);

                    >div {
                        width: 100%;
                    }
                }
            }
        }

        @media (max-width: 1400px) {
            max-height: calc(100vh - 400px);
            height: 100%;
            width: calc(100vw - 200px);
        }

        @media (max-width: 640px) {
            max-height: calc(100vh - 200px);
            height: 100%;
            width: calc(100vw - 80px);
            border-radius: 16px;
        }
    }

    .modal-sizeguide-main {
        position: absolute;
        width: calc((1290px / 6) * 5);
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background-color: var(--white);
        transition: all ease 0.5s;
        opacity: 0;
        visibility: hidden;
        overflow: auto;

        &.open {
            opacity: 1;
            visibility: visible;
        }

        .progress {

            .rc-slider-track,
            .rc-slider-rail {
                height: 8px;
            }

            .rc-slider-handle {
                margin-top: -7px;
                width: 20px;
                height: 20px;

                &.rc-slider-handle-1 {
                    display: none;
                    pointer-events: none;
                }
            }
        }

        table {
            border-collapse: collapse;
            width: 100%;
            margin-top: 20px;

            td,
            th {
                border: 1px solid #d6d6d6;
                text-align: left;
                padding: 8px;
                text-align: center;
            }

            tr:nth-child(even) {
                background-color: #eeeeee;
            }
        }

        @media (max-width: 1200px) {
            width: calc(100vw - 120px);
        }

        @media (max-width: 640px) {
            width: calc(100vw - 80px);
            border-radius: 16px;

            .progress {

                .rc-slider-track,
                .rc-slider-rail {
                    height: 6px;
                }

                .rc-slider-handle {
                    margin-top: -5px;
                    width: 16px;
                    height: 16px;
                }
            }
        }
    }

    .modal-video-main {
        position: absolute;
        width: calc((1290px / 6) * 5);
        height: 605px;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background-color: var(--white);
        transition: all ease 0.5s;
        opacity: 0;
        visibility: hidden;
        overflow: auto;

        &.open {
            opacity: 1;
            visibility: visible;
        }

        iframe {
            width: 100%;
            height: 100%;
        }

        @media (max-width: 1400px) {
            width: calc(100vw - 120px);
            height: 70vh;
        }

        @media (max-width: 640px) {
            height: 30vh;
            width: calc(100vw - 40px);
        }
    }
}

.modal-newsletter {
    z-index: 102;
}

.modal-quickview-block {
    z-index: 103;
}

.modal-wishlist-block {
    z-index: 104;
}

.modal-cart-block {
    z-index: 104;
}

.modal-sizeguide-block {
    z-index: 104;
}

.modal-compare-block {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 200px;
    opacity: 0;
    visibility: hidden;
    transition: all ease 0.4s;
    background: rgba($color: #000000, $alpha: 0.6);
    z-index: 101;

    &:has(.modal-compare-main.open) {
        opacity: 1;
        visibility: visible;
    }

    .modal-compare-main {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: var(--white);
        transform: translateY(100%);
        transition: all ease 0.5s;
        box-shadow: 0px 5px 18px 5px rgba(64, 72, 87, 0.15);

        &.open {
            transform: translateY(0);
        }

        .remove-compare-btn {
            &:hover {
                svg {
                    color: var(--black);
                }
            }
        }

        .list-product {
            @media (max-width: 1290px) {
                max-width: calc(100% - 105px - 238px);
                overflow: unset;
                overflow-x: auto;
                padding-top: 24px;
                margin-top: -24px;
            }
        }

        @media (max-width: 767.98px) {
            height: 320px;

            .list-product {
                max-width: calc(100%);
                overflow: unset;
                overflow-x: auto;
                padding-top: 24px;
                margin-top: -24px;
            }
        }
    }
}
