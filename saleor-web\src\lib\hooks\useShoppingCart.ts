import { useLocale, useTranslations } from "next-intl";
import { executeGraphQL } from "@/lib/utils/graphql";
import {
	CheckoutAddLineDocument,
	CheckoutChargeStatusEnum,
	CheckoutCreateDocument,
	CheckoutFindDocument,
	CheckoutLinesDeleteDocument,
	CheckoutLinesUpdateDocument,
	CheckoutLineUpdateInput,
	FindCheckoutIdDocument,
} from "@/gql/graphql";
import { handleGraphqlLocale } from "@/lib/utils/util";
import { useUserStore } from "@/lib/store/user";
import { invariant } from "ts-invariant";
import { DataType } from "@/lib/@types/base";
import { useUserAuth } from "./useUserAuth";
import { useShoppingCartStore } from "../store/shoppingCart";
import { App } from "antd";
import { useProductStore } from "../store/product.store";
import { deleteCookie } from "cookies-next";

interface AddToCartProps {
	channel: string;
	selectedVariantID: string;
	quantity: number;
}

export const useShoppingCart = () => {
	const locale = useLocale();
	const t = useTranslations();
	const { addCheckoutId, getCheckoutId, removeCheckoutId, setCartList, cartList,BuyBowCartList, BuyBowsetCartList } =
		useShoppingCartStore();
	const { userInfo } = useUserStore();
	const { useSignOut,isLogin } = useUserAuth();
	const { setCurrencyUnit } = useProductStore();
	// 将 message 替换为 App.useApp()
	const { message } = App.useApp();
	/**
	 * 将商品添加到购物车的异步函数。
	 *
	 * @param {AddToCartProps} - 包含添加到购物车所需属性的对象。
	 * @param {string} channel - 渠道标识，用于确定商品的销售平台。
	 * @param {string} selectedVariantID - 选中的商品变体ID，用于确定具体添加到购物车的商品。
	 * @param {number} quantity - 添加的数量，指定购物车中该商品的数量。
	 *
	 * 此函数首先尝试查找或创建一个购物车实例，然后根据选定的商品变体ID和数量，
	 * 使用GraphQL查询将商品添加到购物车。如果selectedVariantID为空，则函数直接返回。
	 * 注意：函数中包含对可能的错误处理的TODO注释。
	 */
	const addToCart = async ({ channel, selectedVariantID, quantity }: AddToCartProps) => {
		if (isLogin()){
      if (!cartList||cartList.lines.length == 0) {
        // console.log(cartList, "zou");
        removeCheckoutId(channel);
      }
    };



		// 从存储中查找到对应渠道的checkoutId
		let checkoutId = getCheckoutId(channel);

		// 如果存储中不存在结账ID，那么根据渠道信息查找或创建��个新的购物车实例。
		if (!checkoutId) {
			const checkout = await createCheckout({ channel: "default-channel" });

			checkoutId = checkout.checkoutCreate.checkout.id || "";
			// 确保购物车实例存在，否则抛出错误。
			// 不存在则抛出错误
			// invariant(checkout, "This should never happen");
		}

		// 如果没有选定的商品变体ID，则直接返回，不执行添加操作。
		if (!selectedVariantID) {
			return false;
		}

		// TODO: 需要添加对执行GraphQL查询过程中可能出现的错误的处理。

		// 使用GraphQL查询将商品变体添加到购物车。
		const { checkoutLinesAdd } = await executeGraphQL(CheckoutAddLineDocument, {
			variables: {
				id: checkoutId,
				productVariantId: decodeURIComponent(selectedVariantID),
				locale: handleGraphqlLocale(locale),
				quantity,
			},
			serverWithAuth: false,
			cache: "no-cache",
		});
		if (checkoutLinesAdd?.errors?.length) {
     message.error(checkoutLinesAdd?.errors[0]?.message || "add To Cart Fail");
      return false;
		}
		message.success("Successfully added");

		setCartList(checkoutLinesAdd?.checkout);
    return checkoutLinesAdd?.checkout.id;
	};
	/**
	 * 异步删除购物车中的指定行项。
	 *
	 * 此函数首先检查用户是否已登录，如果没有登录，则不执行任何操作。
	 * 接着，它尝试获取当前购物车的ID，并确保该ID存在，因为后续的操作需要这个ID。
	 * 然后，它使用GraphQL客户端执行删除操作，传入需要删除的购物车行项ID和其他必要的变量。
	 * 最后，它更新购物车列表，以反映删除操作的结果。
	 *
	 * @param linesIds 要删除的购物车行项ID数组。
	 */
	const removeCartItem = async (linesIds: string[]) => {
		// 检查用户是否已登录，如果没有登录，则直接返回，不执行删除操作。
		// if (!isLogin()) return;
		const id = cartList?.id;
		// 确保获取到的checkoutId存在，如果不存在，则抛出错误。
		invariant(id, "id is not existence!");

		// 执行GraphQL查询，删除指定的购物车行项。
		const { checkoutLinesDelete } = await executeGraphQL(CheckoutLinesDeleteDocument, {
			variables: {
				id,
				linesIds,
				locale: handleGraphqlLocale(locale),
			},
			clientWithAuth: true,
		});
		if (checkoutLinesDelete?.errors?.length) {
			return message.error(checkoutLinesDelete?.errors[0]?.message || "remove Cart Item Fail");
		}

		if (linesIds.length == 1) {
			message.success("remove succeed");
		}

		// 更新购物车列表，反映删除操作的结果���
		findCheckout("default-channel");
	};

	/**
	 * 异步更新购物车行数。
	 *
	 * 该函数通过GraphQL客户端更新特定结账ID的购物车行数。如果用户未登录，则函数直接返回。
	 * 它执行一个GraphQL查询来更新购物车行数，并处理任何由此产生的错误。
	 * 如果更新成功，它将更新的购物车数据存储在本地状态中。
	 *
	 * @param {string} checkoutId - 结账ID，用于标识要更新的购物车。
	 * @param {CheckoutLineUpdateInput[]} lines - 要更新的购物车行数据数组。
	 * @throws 如果更新过程中发生错误，则抛出错误。
	 */
	const updateLinesCount = async ({
		checkoutId,
		lines,
	}: {
		checkoutId: string;
		lines: CheckoutLineUpdateInput[];
	}) => {
		// 检查用户是否已登录，如果没有登录则直接返回。
		// if (!isLogin()) return;

		// 执行GraphQL查询来更新购物车行数，使用提供的checkoutId和lines。
		const { checkoutLinesUpdate } = await executeGraphQL(CheckoutLinesUpdateDocument, {
			variables: {
				id: checkoutId,
				lines,
				locale: handleGraphqlLocale(locale),
			},
			clientWithAuth: true, // 使用具有认证的GraphQL客户端。
			cache: "no-cache", // 禁止使用缓存以确保获取最新数据。
		});

		// 如果更新过程中有错误，抛出第一个错误的消息，或者如果消息不存在，则抛出通用错误消息。
		if (checkoutLinesUpdate?.errors?.length) {
			return message.error(checkoutLinesUpdate?.errors[0]?.message || "update Cart Item Fail");
		}
		// console.log(checkoutLinesUpdate, "checkoutLinesUpdate");

		findCheckout("default-channel");
		// 如果更新成功，更新本地购物车列表状态。
	};

	/**
	 * 异步函数：查找或创建结账ID。
	 *
	 * 该函数通过GraphQL查询尝试获取当前用户的结账ID。如果用户已有结账ID，则返回第一个ID；
	 * 否则，返回空字符串。如果查询过程中发生错误，也返回空字符串。
	 *
	 * @param channel 渠道标识，用于指定查询或创建的结账ID所属的渠道。
	 * @returns 返回用户的结账ID（如果存在）或空字符串（如果不存在或查询失败）。
	 */
	const findOrCreateCheckoutId = async (channel: string) => {
		try {
			// 执行GraphQL查询，尝试获取用户的结账ID。
			const { me } = await executeGraphQL(FindCheckoutIdDocument, {
				variables: {
					channel,
				},
				clientWithAuth: true,
			});
			const node = me?.checkouts?.edges?.at(-1)?.node;
			let checkoutId = node?.chargeStatus === CheckoutChargeStatusEnum["None"] ? node?.id : "";
			if (!checkoutId) {
				checkoutId = (await createCheckout({ channel }))?.checkoutCreate?.checkout?.id || "";
			}
			addCheckoutId(channel, checkoutId);
			return checkoutId;
		} catch {
			// 如果查询过程中发生错误，返回空字符串。
			return "";
		}
	};

	/**
	 * 创建一个结账流程。
	 *
	 * 该函数通过GraphQL查询创建一个结账流程。它使用了预定义的GraphQL文档CheckoutCreateDocument，
	 * 并传入特定的参数来执行创建操作。这个函数特别之处在于它要求调用者具有授权，
	 * 并且强制不使用缓存，以确保结账流程的创建是实时的和安全的。
	 *
	 * @param {string} channel - 结账渠道。这个参数指定了结账流程将通过哪个渠道进行。
	 *                          它可以是任何字符串，具体取决于应用程序的实现。
	 * @returns 返回执行GraphQL查询的结果。这个结果可能包含关于新创建的结账流程的信息，
	 *          例如结账ID或者创建过程中的任何错误信息。
	 */
	const createCheckout = async ({ channel }: { channel: string }) => {

		let Checkout=await executeGraphQL(CheckoutCreateDocument, {
			clientWithAuth: true,
			cache: "no-cache",
			variables: {
				channel,
				locale: handleGraphqlLocale(locale),
			},
		});
    if(!Checkout.checkoutCreate){
        deleteCookie("__user__login__info");
        deleteCookie("user-store");
        useSignOut();
    }
    return Checkout;
	};

	/**
	 * 异步查找并返回购物车信息。
	 * 购物车列表
	 *
	 * 该函数首先检查用户是否已登录，如果没有登录，则抛出错误。
	 * 接着，它尝试从本地存储或通过GraphQL查询获取购物车ID。
	 * 如果购物车ID存在，它将执行一个GraphQL查询来获取购物车详情。
	 * 如果购物车ID不存在，它将尝试创建一个新的购物车ID，并再次执行查询。
	 * 最后，它更新本地存储中的购物车ID和购物车列表，并返回购物车详情。
	 *
	 * @param channel 渠道标识，用于确定查找购物车的方式。
	 * @returns 返回购物车详情，如果未找到则返回null。
	 */
	const findCheckout = async (
		channel: string,
	): Promise<DataType<typeof CheckoutFindDocument>["checkout"]> => {
		try {
			// 检查用户是否已登录，如果没有登录，则抛出错误。
			// if (!isLogin()) throw new Error("Not Login");

			// 尝试从本地存储获取购物车ID，如果不存在，则尝试创建一个新的购物车ID。
			const id = getCheckoutId(channel) || (await findOrCreateCheckoutId(channel));

			// 如果购物车ID存在，执行GraphQL查询来获取购物车详���。
			const { checkout } = id
				? await executeGraphQL(CheckoutFindDocument, {
						clientWithAuth: true,
						variables: {
							id,
							locale: handleGraphqlLocale(locale),
						},
						cache: "no-cache",
					})
				: { checkout: null };

			// 更新本地存储中的购物车ID，并设置购物车列表。
			addCheckoutId(channel, checkout?.id || "");
			setCartList(checkout);

			// 获取购物车 更新符号
			setCurrencyUnit(checkout.lines[0].totalPrice.gross.currency || "USD");

			// 返回购物车详情。
			return checkout;
		} catch {
			// 捕获并忽略任何错误，这些错误可能包括用户未登录、购物车ID无效或购物车未找到等。
			// 捕获并忽略查询中可能出现的任何错误，例如无效的ID或结账未找到。
			// 我们忽略无效ID或未找到结账
		}
	};

	////////////////////////////////////
	// 立即购买的函数

	/**
	 * 立即购买函数
	 * 创建新的购物车并添加商品
	 */
	async function createBuyBowCheckout({ channel, selectedVariantID, quantity }: AddToCartProps) {
		try {
			// 检查必要参数
			if (!selectedVariantID) {
				throw new Error("Product variant ID is required");
			}

			// if (!isLogin()) {
			// 	throw new Error("not Login");
			// }

			// 1. 创建新的购物车
			const checkout = await createCheckout({ channel: "default-channel" });
			if (!checkout?.checkoutCreate?.checkout?.id) {
        message.error("Failed to process buy now");
        return false;
			}

			const checkoutId = checkout.checkoutCreate.checkout.id;

			// 2. 将商品添加到新购物车
			const { checkoutLinesAdd } = await executeGraphQL(CheckoutAddLineDocument, {
				variables: {
					id: checkoutId,
					productVariantId: decodeURIComponent(selectedVariantID),
					locale: handleGraphqlLocale(locale),
					quantity,
				},
				serverWithAuth: false,
				cache: "no-cache",
			});

			// 检查是否有错误
			if (checkoutLinesAdd?.errors?.length) {

				throw new Error(checkoutLinesAdd.errors[0].message || "Failed to add product to cart");
			}

      
			// 3. 存储购物车数据
			BuyBowsetCartList(checkoutLinesAdd.checkout);

			return checkoutLinesAdd.checkout;
		} catch (error) {
			message.error(error.message || "Failed to process buy now");
			return false;
		}


	}

  const BuyBowremoveCartItem = async (linesIds: string[]) => {
    // 检查用户是否已登录，如果没有登录，则直接返回，不执行删除操作。
    // if (!isLogin()) return;
    const id = BuyBowCartList?.id;
    // 确保获取到的checkoutId存在，如果不存在，则抛出错误。
    invariant(id, "id is not existence!");

    // 执行GraphQL查询，删除指定的购物车行项。
    const { checkoutLinesDelete } = await executeGraphQL(CheckoutLinesDeleteDocument, {
      variables: {
        id,
        linesIds,
        locale: handleGraphqlLocale(locale),
      },
      clientWithAuth: true,
    });
    if (checkoutLinesDelete?.errors?.length) {
      return message.error(checkoutLinesDelete?.errors[0]?.message || "remove Cart Item Fail");
    }

    if (linesIds.length == 1) {
      message.success("remove succeed");
    }

    // 更新购物车列表，反映删除操作的结果���
    BuyBowfindCheckout("default-channel");
  };
	const BuyBowfindCheckout = async (
		channel: string,
	): Promise<DataType<typeof CheckoutFindDocument>["checkout"]> => {
		try {
			// 检查用户是否已登录，如果没有登录，则抛出错误。
			// if (!isLogin()) throw new Error("Not Login");

			// 尝试从本地存储获取购物车ID，如果不存在，则尝试创建一个新的购物车ID。
      const id = BuyBowCartList?.id;

			// 如果购物车ID存在，执行GraphQL查询来获取购物车详���。
			const { checkout } = id
				? await executeGraphQL(CheckoutFindDocument, {
						clientWithAuth: true,
						variables: {
							id,
							locale: handleGraphqlLocale(locale),
						},
						cache: "no-cache",
					})
				: { checkout: null };

			// 3. 存储购物车数据
			BuyBowsetCartList(checkout);
			// 返回购物车详情。
			return checkout;
		} catch {
			// 捕获并忽略任何错误，这些错误可能包括用户未登录、购物车ID无效或购物车未找到等。
			// 捕获并忽略查询中可能出现的任何错误，例如无效的ID或结账未找到。
			// 我们忽略无效ID或未找到结账
		}
	};
	const BuyBowupdateLinesCount = async ({
		checkoutId,
		lines,
	}: {
		checkoutId: string;
		lines: CheckoutLineUpdateInput[];
	}) => {
		// 检查用户是否已登录，如果没有登录则直接返回。
		// if (!isLogin()) return;

		// 执行GraphQL查询来更新购物车行数，使用提供的checkoutId和lines。
		const { checkoutLinesUpdate } = await executeGraphQL(CheckoutLinesUpdateDocument, {
			variables: {
				id: checkoutId,
				lines,
				locale: handleGraphqlLocale(locale),
			},
			clientWithAuth: true, // 使用具有认证的GraphQL客户端。
			cache: "no-cache", // 禁止使用缓存以确保获取最新数据。
		});

		// 如果更新过程中有错误，抛出第一个错误的消息，或者如果消息不存在，则抛出通用错误消息。
		if (checkoutLinesUpdate?.errors?.length) {
			return message.error(checkoutLinesUpdate?.errors[0]?.message || "update Cart Item Fail");
		}
    BuyBowfindCheckout("default-channel");
	};
	return {
		addToCart,
		findCheckout,
		findOrCreateCheckoutId,
		createCheckout,
		removeCartItem,
		updateLinesCount,

		//立即支付
		createBuyBowCheckout,
    BuyBowremoveCartItem,
    BuyBowupdateLinesCount,
    BuyBowfindCheckout
	};
};
